#!/usr/bin/env python3
import requests

# Test the task listing endpoint
response = requests.get('http://127.0.0.1:8000/tasks/')
if response.status_code == 200:
    data = response.json()
    print(f'✅ Task listing successful: {len(data["tasks"])} tasks found')
    if data['tasks']:
        latest_task = data['tasks'][0]
        print(f'   Latest task: {latest_task["id"][:8]}... ({latest_task["status"]})')
        
        # Test task logs
        logs_response = requests.get(f'http://127.0.0.1:8000/tasks/{latest_task["id"]}/logs')
        if logs_response.status_code == 200:
            logs_data = logs_response.json()
            print(f'✅ Task logs retrieved: {len(logs_data["logs"])} log entries')
            print('   Recent logs:')
            for log in logs_data["logs"][:3]:
                print(f'     [{log["level"]}] {log["message"]}')
        else:
            print(f'❌ Task logs failed: {logs_response.status_code}')
else:
    print(f'❌ Task listing failed: {response.status_code}')
