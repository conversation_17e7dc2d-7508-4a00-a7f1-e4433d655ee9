# Database Migration Solution - Final Instructions

## 🎯 Issue Confirmed

**Error:** `Could not find the 'domain_property' column of 'sites' in the schema cache`

**Root Cause:** Your Supabase database is missing the configuration columns needed for the new site management features.

**Current Database State:**
- **Project:** `ltrymguxcxzyofxmbutv` (https://ltrymguxcxzyofxmbutv.supabase.co)
- **Current Columns:** `id`, `domain`, `created_at`
- **Missing Columns:** `domain_property`, `ga_property_id`, `service_account_data`, `homepage`, `last_updated`

## 🔧 **SOLUTION: Manual Migration Required**

Since the project is not accessible via the Supabase Management API, you need to run the migration manually in the Supabase dashboard.

### **Step 1: Open Supabase Dashboard**
1. **Go to:** https://supabase.com/dashboard
2. **Sign in** to your Supabase account
3. **Find and select** project `ltrymguxcxzyofxmbutv`

### **Step 2: Open SQL Editor**
1. **Click "SQL Editor"** in the left sidebar
2. **Click "New Query"** to create a new SQL script

### **Step 3: Run Migration SQL**
**Copy and paste this exact SQL:**

```sql
-- Add missing columns to sites table
ALTER TABLE sites
ADD COLUMN IF NOT EXISTS domain_property TEXT,
ADD COLUMN IF NOT EXISTS ga_property_id TEXT,
ADD COLUMN IF NOT EXISTS service_account_data JSONB,
ADD COLUMN IF NOT EXISTS homepage TEXT,
ADD COLUMN IF NOT EXISTS last_updated TIMESTAMPTZ DEFAULT NOW();

-- Update existing records
UPDATE sites SET last_updated = created_at WHERE last_updated IS NULL;

-- Verify the migration
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'sites' 
ORDER BY ordinal_position;
```

### **Step 4: Execute and Verify**
1. **Click "Run"** (or press Ctrl+Enter)
2. **Check the results** - should show success message
3. **Verify columns** - should see all 8 columns listed

**Expected Result:**
```
column_name          | data_type
---------------------|---------------------------
id                   | integer
domain               | text
created_at           | timestamp with time zone
domain_property      | text                    ← NEW
ga_property_id       | text                    ← NEW
service_account_data | jsonb                   ← NEW
homepage             | text                    ← NEW
last_updated         | timestamp with time zone ← NEW
```

### **Step 5: Restart API Server**
1. **Stop current server** (Ctrl+C in terminal)
2. **Restart server:**
   ```bash
   cd C:/Gautam/Projects/Scraper
   python api_refactored.py
   ```

### **Step 6: Test Configuration Update**
1. **Open:** http://localhost:8000
2. **Click gear (⚙️)** on boernevisioncenter.com
3. **Click "Edit Configuration"**
4. **Fill in the form:**
   - Domain Property: `https://boernevisioncenter.com/`
   - GA Property ID: `[Your actual GA property ID]`
   - Upload service account JSON file
   - Homepage: `https://boernevisioncenter.com/`
5. **Click "Save Changes"**

**Expected Success Response:**
```json
{
  "success": true,
  "message": "Configuration updated successfully for boernevisioncenter.com",
  "domain": "boernevisioncenter.com",
  "site_id": "1"
}
```

## 🔍 **Verification Steps**

### **Check Migration Success**
Run this in Supabase SQL Editor:
```sql
-- Check that boernevisioncenter.com can be updated
SELECT 
    id,
    domain,
    domain_property,
    ga_property_id,
    homepage,
    CASE 
        WHEN domain_property IS NOT NULL AND ga_property_id IS NOT NULL 
        THEN 'Configured' 
        ELSE 'Needs Configuration' 
    END as status
FROM sites 
WHERE domain = 'boernevisioncenter.com';
```

### **Test API Endpoint**
```bash
curl "http://localhost:8000/sites/1/info"
```

**Expected Response After Migration:**
```json
{
  "success": true,
  "site_info": {
    "site_id": 1,
    "domain": "boernevisioncenter.com",
    "domain_property": null,
    "ga_property_id": null,
    "homepage": null,
    "created_at": "2025-06-24T17:14:12.009354+00:00",
    "last_updated": "2025-06-24T17:14:12.009354+00:00",
    "has_service_account": false
  }
}
```

## 🚀 **After Successful Migration**

### **What Changes**
1. **Site shows "⚠️ Setup needed"** - Ready for configuration
2. **"Edit Configuration" works** - No more column errors
3. **Configuration can be saved** - Stored in new columns
4. **One-click re-analysis** - Available after configuration

### **User Workflow**
1. **Add Configuration:** Use "Edit Configuration" to set up boernevisioncenter.com
2. **Site becomes "✅ Configured"** - Ready for re-analysis
3. **One-click re-analysis** - No need to re-enter details
4. **Full site management** - Edit, clear, delete options available

## 🔧 **Troubleshooting**

### **If Migration Fails**
- **Check permissions:** Ensure you have admin access to the Supabase project
- **Run commands separately:** Execute each ALTER TABLE statement individually
- **Check project:** Verify you're in the correct project `ltrymguxcxzyofxmbutv`

### **If Configuration Update Still Fails**
1. **Verify columns exist:** Run the verification SQL
2. **Check server logs:** Look for detailed error messages
3. **Restart server:** Ensure it picks up the schema changes
4. **Test API endpoint:** Use `/sites/1/info` to check site status

### **If Site Shows Wrong Status**
1. **Refresh browser:** Clear cache and reload page
2. **Check database:** Run verification queries
3. **Restart API:** Ensure server has latest schema

## 📊 **Migration Summary**

### **Before Migration**
```
sites table:
├── id (integer)
├── domain (text)
└── created_at (timestamp)

Status: ❌ Configuration updates fail
Error: "Could not find the 'domain_property' column"
```

### **After Migration**
```
sites table:
├── id (integer)
├── domain (text)
├── created_at (timestamp)
├── domain_property (text)        ← NEW
├── ga_property_id (text)         ← NEW
├── service_account_data (jsonb)  ← NEW
├── homepage (text)               ← NEW
└── last_updated (timestamp)      ← NEW

Status: ✅ Configuration updates work
Feature: Full site management available
```

## 🎉 **Expected Final Result**

After successful migration and configuration:

1. **✅ "Edit Configuration" works** without column errors
2. **✅ boernevisioncenter.com can be configured** with GA property and service account
3. **✅ Site shows "Configured" badge** after setup
4. **✅ "Re-analyze" works** with one click
5. **✅ All site management features** fully functional

**The database migration will resolve the column error and enable full site management capabilities!** 🚀

---

## 📋 **Quick Reference**

**SQL to run in Supabase dashboard:**
```sql
ALTER TABLE sites
ADD COLUMN IF NOT EXISTS domain_property TEXT,
ADD COLUMN IF NOT EXISTS ga_property_id TEXT,
ADD COLUMN IF NOT EXISTS service_account_data JSONB,
ADD COLUMN IF NOT EXISTS homepage TEXT,
ADD COLUMN IF NOT EXISTS last_updated TIMESTAMPTZ DEFAULT NOW();

UPDATE sites SET last_updated = created_at WHERE last_updated IS NULL;
```

**Project Details:**
- URL: https://ltrymguxcxzyofxmbutv.supabase.co
- Project ID: ltrymguxcxzyofxmbutv
- Table: sites
- Missing columns: 5 configuration columns
