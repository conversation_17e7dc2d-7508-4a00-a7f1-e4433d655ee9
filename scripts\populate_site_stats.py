#!/usr/bin/env python3
"""
Populate cached site statistics for existing sites
Run this once after adding the stats columns to populate existing data
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.config import settings
from supabase import create_client
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def populate_site_stats():
    """Populate cached statistics for all existing sites"""
    
    if not settings.supabase_url or not settings.supabase_key:
        logger.error("Supabase credentials not configured")
        return False
    
    try:
        client = create_client(settings.supabase_url, settings.supabase_key)
        
        # Get all sites
        sites_response = client.table('sites').select('id, domain').execute()
        sites = sites_response.data or []
        
        logger.info(f"Found {len(sites)} sites to update")
        
        for site in sites:
            site_id = site['id']
            domain = site['domain']
            
            logger.info(f"Updating stats for site: {domain} ({site_id})")
            
            try:
                # Get actual counts from data tables
                pages_count = client.table('pages').select('id', count='exact').eq('site_id', site_id).execute().count or 0
                keywords_count = client.table('gsc_keywords').select('id', count='exact').eq('site_id', site_id).execute().count or 0
                links_count = client.table('internal_links').select('id', count='exact').eq('site_id', site_id).execute().count or 0
                traffic_count = client.table('gsc_traffic').select('id', count='exact').eq('site_id', site_id).execute().count or 0
                
                # Count external links (if they exist)
                try:
                    external_links_count = client.table('external_links').select('id', count='exact').eq('site_id', site_id).execute().count or 0
                except:
                    external_links_count = 0

                # Update the sites table with cached stats
                update_data = {
                    'stats_pages': pages_count,
                    'stats_keywords': keywords_count,
                    'stats_internal_links': links_count,
                    'stats_traffic_records': traffic_count,
                    'stats_external_links': external_links_count,
                    'stats_last_updated': 'now()'
                }
                
                result = client.table('sites').update(update_data).eq('id', site_id).execute()
                logger.info(f"✅ {domain}: {pages_count} pages, {keywords_count} keywords, {links_count} links, {traffic_count} traffic")
                
            except Exception as e:
                logger.error(f"❌ Error updating stats for {domain}: {e}")
                continue
        
        logger.info("✅ Finished populating site statistics")
        return True
        
    except Exception as e:
        logger.exception("Error populating site stats:")
        return False

if __name__ == "__main__":
    asyncio.run(populate_site_stats())
