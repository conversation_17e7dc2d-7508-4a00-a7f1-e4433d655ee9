# Tests

This directory contains all tests for the SEO Analysis Tool, organized by category.

## Test Structure

```
tests/
├── unit/           # Unit tests for individual components
├── integration/    # Integration tests for system components  
├── functional/     # End-to-end functional tests
├── debug/          # Debug and troubleshooting scripts
├── fixtures/       # Test data and fixtures
└── conftest/       # Pytest configuration and shared fixtures
```

## Test Categories

### Unit Tests (`tests/unit/`)
Tests for individual functions and classes in isolation.

- `test_data_types.py` - Data type validation and conversion tests
- `test_text_processing.py` - HTML/text processing utility tests

### Integration Tests (`tests/integration/`)
Tests for interactions between components.

- `test_excel_generation.py` - Excel report generation tests
- `test_report_service.py` - Report service integration tests
- `test_wordpress_api.py` - WordPress API integration tests
- `test_ga_aggregation.py` - Google Analytics data aggregation tests

### Functional Tests (`tests/functional/`)
End-to-end tests that verify complete workflows.

- `test_data_sheet_generation.py` - Complete data sheet generation workflow
- `test_database_operations.py` - Database save/retrieve operations

### Debug Scripts (`tests/debug/`)
Troubleshooting and debugging utilities.

- `debug_clear_data.py` - Clear test data from database
- `debug_database_save.py` - Debug database save issues
- `debug_file_upload.py` - Debug file upload problems
- `debug_pages_upsert.py` - Debug page insertion issues
- `debug_ga_data_fix.py` - Debug GA data aggregation

## Running Tests

### Run All Tests
```bash
python -m pytest tests/
```

### Run Specific Category
```bash
# Unit tests only
python -m pytest tests/unit/

# Integration tests only
python -m pytest tests/integration/

# Functional tests only
python -m pytest tests/functional/
```

### Run Specific Test File
```bash
python -m pytest tests/unit/test_data_types.py
```

### Run with Coverage
```bash
python -m pytest tests/ --cov=src --cov-report=html
```

### Run Debug Scripts
```bash
# Clear test data
python tests/debug/debug_clear_data.py

# Debug database issues
python tests/debug/debug_database_save.py
```

## Test Configuration

Tests use pytest with the following configuration:
- Automatic test discovery
- Fixtures for common test data
- Coverage reporting
- Parallel test execution (when possible)

## Writing New Tests

### Unit Tests
- Test individual functions/methods
- Mock external dependencies
- Focus on edge cases and error conditions

### Integration Tests  
- Test component interactions
- Use real dependencies when possible
- Verify data flow between components

### Functional Tests
- Test complete user workflows
- Use realistic test data
- Verify end-to-end functionality

## Test Data

Test fixtures and data are stored in `tests/fixtures/`:
- Sample configuration files
- Mock API responses
- Test database schemas
- Example input/output data

## Continuous Integration

Tests are automatically run on:
- Pull requests
- Main branch commits
- Release builds

All tests must pass before code can be merged.
