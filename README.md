# SEO Analysis Tool

A comprehensive tool for analyzing websites, fetching Google Search Console and Google Analytics data, and generating SEO reports with Supabase integration.

## 🚀 Project Structure

```
src/
├── __init__.py
├── config/                 # Configuration management
│   ├── __init__.py
│   └── settings.py        # Pydantic settings with env support
├── models/                # Data models and schemas
│   ├── __init__.py
│   └── schemas.py         # Pydantic models for API/data
├── core/                  # Core business logic
│   ├── __init__.py
│   ├── crawler.py         # Web crawling functionality
│   ├── google_apis.py     # GSC/GA API clients
│   └── wordpress.py       # WordPress API integration
├── database/              # Database integrations
│   ├── __init__.py
│   └── supabase_client.py # Supabase client
├── services/              # High-level business services
│   ├── __init__.py
│   ├── analysis_service.py    # Main analysis orchestration
│   ├── link_analysis_service.py # Internal link analysis
│   └── report_service.py      # Report generation
├── utils/                 # Utility functions
│   ├── __init__.py
│   ├── logging.py         # Logging configuration
│   ├── text_processing.py # Text/HTML processing
│   └── file_utils.py      # File handling utilities
├── api/                   # FastAPI application
│   ├── __init__.py
│   ├── app.py            # FastAPI app setup
│   └── routes.py         # API route definitions
└── cli/                   # Command-line interface
    ├── __init__.py
    └── main.py           # CLI entry point
```

## 🧹 Project Cleanup

This repository includes a cleanup script to organize the project structure according to best practices. The script:

1. Creates proper directory structure
2. Moves files to correct locations
3. Archives temporary/redundant files
4. Updates imports and references
5. Cleans up temporary files
6. Creates a proper .gitignore

### Running the Cleanup Script

```bash
python cleanup_project.py
```

After running the cleanup script, verify that everything works correctly:

```bash
python test_cleanup.py
```

### Organizing Tests

A separate script is provided to organize tests into a proper structure:

```bash
python organize_tests.py
```

This script:
1. Creates a proper test directory structure (unit, integration, functional)
2. Moves test files to appropriate categories
3. Creates comprehensive test documentation
4. Generates pytest configuration
5. Creates a test runner script

After organizing tests, you can run them with:

```bash
python run_tests.py all       # Run all tests
python run_tests.py unit      # Run unit tests only
python run_tests.py coverage  # Run with coverage report
```

## 📋 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Environment

Copy the example environment file and customize:

```bash
cp .env.example .env
# Edit .env with your settings
```

### 3. Run the Application

**Option A: API Server (Recommended)**
```bash
python main.py
```

**Option B: CLI Analysis**
```bash
python -m src.cli.main analyze config.json
```

### 4. Access the Dashboard

Open your browser to: http://localhost:8000

## 📊 Features

### ✅ Web Crawling
- JavaScript-rendered content support (Playwright)
- Configurable timeouts and concurrency
- Automatic URL discovery from homepage
- Failed URL tracking and retry logic

### ✅ Google Search Console Integration
- Keyword performance data
- Historical traffic analysis
- CTR and position metrics
- Date range filtering

### ✅ Google Analytics Integration
- Page view metrics
- User engagement data
- Traffic source analysis
- Custom dimension support

### ✅ WordPress API Integration
- Content extraction
- SEO metadata analysis
- Internal link discovery
- Incremental updates

### ✅ Supabase Database
- Structured data storage
- Efficient querying
- Multi-site management
- Historical data tracking

### ✅ Excel Report Generation
- Customizable templates
- Data filtering options
- Multi-sheet reports
- Formatted visualizations

## 🔧 Development

### Project Organization

- **src/**: All source code
- **docs/**: Documentation files
- **migrations/**: Database migration scripts
- **config/**: Configuration files
- **tests/**: Test scripts
- **public/**: Static web files

### Adding New Features

1. **New API Endpoint**: Add to `src/api/routes.py`
2. **New Service**: Create in `src/services/`
3. **New Data Source**: Add client to `src/core/`
4. **New Report Type**: Extend `src/services/report_service.py`

## 📚 Documentation

Detailed documentation is available in the `docs/` directory:

- [API Documentation](docs/API_DOCUMENTATION.md)
- [Supabase Database Documentation](docs/SUPABASE_DATABASE_DOCUMENTATION.md)
- [Deployment Guide](docs/DEPLOYMENT_GUIDE.md)

## 🚀 Deployment

This project is configured for deployment on Render.com:

```bash
# Deploy to Render
render deploy
```

See [render.yaml](render.yaml) for deployment configuration.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
