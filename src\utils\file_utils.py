"""
File handling utilities
"""
import os
import json
import tempfile
import uuid
from typing import Dict, Any, Optional
from pathlib import Path

from src.config.settings import settings
from src.utils.logging import get_logger

logger = get_logger(__name__)


def ensure_directory(path: str) -> None:
    """Ensure a directory exists, create if it doesn't"""
    os.makedirs(path, exist_ok=True)


def cleanup_old_directories(base_dir: str, max_age_hours: int = 48) -> None:
    """Clean up old session-specific directories"""
    import time
    import shutil
    from pathlib import Path

    try:
        base_path = Path(base_dir)
        if not base_path.exists():
            return

        current_time = time.time()
        cutoff_time = current_time - (max_age_hours * 3600)

        # Look for directories with session patterns (containing timestamps and UUIDs)
        for dir_path in base_path.iterdir():
            if dir_path.is_dir():
                try:
                    # Check if directory is old enough to be cleaned up
                    if dir_path.stat().st_mtime < cutoff_time:
                        # Additional check: only clean up directories that look like session directories
                        dir_name = dir_path.name
                        if ('_' in dir_name and
                            any(char.isdigit() for char in dir_name) and
                            len(dir_name.split('_')) >= 3):  # reports_domain_sessionid_timestamp pattern
                            shutil.rmtree(dir_path)
                            logger.info(f"Cleaned up old session directory: {dir_path}")
                except Exception as e:
                    logger.warning(f"Failed to cleanup old directory {dir_path}: {e}")

    except Exception as e:
        logger.error(f"Error during directory cleanup: {e}")


def create_temp_file(content: str, suffix: str = '.json') -> str:
    """Create a temporary file with the given content"""
    temp_file = os.path.join(settings.temp_dir, f"temp_{uuid.uuid4()}{suffix}")
    ensure_directory(settings.temp_dir)
    
    with open(temp_file, 'w') as f:
        f.write(content)
    
    return temp_file


def create_temp_json_file(data: Dict[str, Any]) -> str:
    """Create a temporary JSON file with the given data"""
    content = json.dumps(data, indent=2)
    return create_temp_file(content, '.json')


def cleanup_temp_file(file_path: str) -> None:
    """Safely remove a temporary file"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.debug(f"Cleaned up temporary file: {file_path}")
    except Exception as e:
        logger.warning(f"Failed to cleanup temporary file {file_path}: {e}")


def cleanup_old_temp_files(max_age_hours: int = 24) -> None:
    """Clean up temporary files older than max_age_hours"""
    import time
    from pathlib import Path

    try:
        temp_dir = Path(settings.temp_dir)
        if not temp_dir.exists():
            return

        current_time = time.time()
        cutoff_time = current_time - (max_age_hours * 3600)

        for file_path in temp_dir.glob("temp_*"):
            try:
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    logger.debug(f"Cleaned up old temporary file: {file_path}")
            except Exception as e:
                logger.warning(f"Failed to cleanup old temporary file {file_path}: {e}")

    except Exception as e:
        logger.error(f"Error during temporary file cleanup: {e}")


def create_temp_file_with_cleanup(content: str, suffix: str = '.json', cleanup_after_hours: int = 1) -> str:
    """Create a temporary file with automatic cleanup scheduling"""
    import threading
    import time

    temp_file = create_temp_file(content, suffix)

    def delayed_cleanup():
        time.sleep(cleanup_after_hours * 3600)  # Convert hours to seconds
        cleanup_temp_file(temp_file)

    # Schedule cleanup in background thread
    cleanup_thread = threading.Thread(target=delayed_cleanup, daemon=True)
    cleanup_thread.start()

    return temp_file


def get_output_directory(domain: str, session_id: str = None) -> str:
    """Get the output directory for a domain with optional session isolation"""
    from urllib.parse import urlparse
    from datetime import datetime

    domain_clean = urlparse(domain).netloc.replace('.', '_')

    if session_id:
        # Create session-specific directory to prevent conflicts
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = os.path.join(settings.reports_dir, f"reports_{domain_clean}_{session_id}_{timestamp}")
    else:
        # Fallback to domain-only directory (legacy behavior)
        output_dir = os.path.join(settings.reports_dir, f"reports_{domain_clean}")

    ensure_directory(output_dir)
    return output_dir


def get_unique_excel_filename(domain: str, report_type: str = "report", date_filter: str = None) -> str:
    """Generate a unique Excel filename to prevent conflicts"""
    from urllib.parse import urlparse
    from datetime import datetime

    domain_clean = urlparse(domain).netloc.replace('.', '_')
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    unique_id = str(uuid.uuid4())[:8]  # Short UUID for uniqueness

    if date_filter:
        filename = f"{report_type}_{domain_clean}_{date_filter}_{timestamp}_{unique_id}.xlsx"
    else:
        filename = f"{report_type}_{domain_clean}_{timestamp}_{unique_id}.xlsx"

    return filename


def save_urls_list(urls: list, output_dir: str, filename: str = 'urls_to_crawl.txt') -> None:
    """Save a list of URLs to a text file"""
    file_path = os.path.join(output_dir, filename)
    with open(file_path, 'w') as f:
        for url in urls:
            f.write(f"{url}\n")
    logger.info(f"Saved {len(urls)} URLs to {file_path}")


def load_config_file(config_path: str) -> Optional[Dict[str, Any]]:
    """Load configuration from a JSON file"""
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        logger.info(f"Loaded configuration from {config_path}")
        return config
    except Exception as e:
        logger.error(f"Failed to load configuration from {config_path}: {e}")
        return None


def save_config_file(config: Dict[str, Any], config_path: str) -> bool:
    """Save configuration to a JSON file"""
    try:
        ensure_directory(os.path.dirname(config_path))
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        logger.info(f"Saved configuration to {config_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to save configuration to {config_path}: {e}")
        return False
