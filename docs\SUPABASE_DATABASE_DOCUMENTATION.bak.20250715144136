# Supabase Database Schema Documentation

## Overview
The Supabase database stores SEO analysis data for multiple websites, including crawled page data, Google Search Console metrics, Google Analytics data, and internal link analysis. The database consists of **6 core tables** and **3 computed views** for efficient data retrieval and reporting.

---

## 🗂️ **Core Tables**

### 1. **`sites`** - Site Configuration & Management
**Purpose:** Central registry of all websites being analyzed with their API configurations.

| Column | Type | Nullable | Description |
|--------|------|----------|-------------|
| `id` | bigint | NO | Primary key, auto-increment |
| `domain` | text | NO | Website domain (e.g., "example.com") |
| `created_at` | timestamp with time zone | NO | Site creation timestamp (default: now()) |
| `domain_property` | text | YES | Google Search Console property URL |
| `ga_property_id` | text | YES | Google Analytics 4 property ID |
| `service_account_data` | jsonb | YES | Google service account credentials (JSON) |
| `homepage` | text | YES | Full homepage URL |
| `last_updated` | timestamp with time zone | YES | Last analysis timestamp (default: now()) |
| `wp_api_key` | text | YES | WordPress API key for internal links |

**Data Source:** 
- Created via API routes when adding new sites
- Updated through site management interface
- Code: `src/database/supabase_client.py` - `create_site()` method

**Key Relationships:** Referenced by all other tables via `site_id` foreign key

---

### 2. **`pages`** - Website Page Data & SEO Metadata
**Purpose:** Stores crawled page content, SEO elements, and metadata for each analyzed page.

| Column | Type | Nullable | Description |
|--------|------|----------|-------------|
| `id` | bigint | NO | Primary key, auto-increment |
| `site_id` | bigint | YES | Foreign key to sites table |
| `URL` | text | NO | Full page URL |
| `snapshot_date` | date | NO | Date when page was crawled |
| `SEO Title` | text | YES | HTML title tag content |
| `Meta Description` | text | YES | Meta description tag content |
| `H1` | text | YES | Main H1 heading text |
| `Page Content` | text | YES | Extracted text content from page |
| `Focus Keyword` | text | YES | Target keyword (from WordPress API) |
| `Page Type` | text | YES | Content type classification |
| `Topic` | text | YES | Content topic classification |
| `Title Length` | integer | YES | Character count of title tag |
| `GSC Clicks` | integer | YES | Aggregated GSC clicks (computed) |
| `GSC Impressions` | integer | YES | Aggregated GSC impressions (computed) |
| `CTR` | numeric | YES | Click-through rate from GSC data (clicks/impressions) |
| `Position` | numeric | YES | Weighted average position from GSC data |
| `Google Analytics Page Views` | integer | YES | Aggregated GA page views (computed) |
| `url_hash` | text | YES | MD5 hash of URL for deduplication |
| `raw_html` | text | YES | Complete HTML source code |

**Data Source:**
- Website crawling via `src/core/crawler.py`
- Saved via `src/database/supabase_client.py` - `save_pages_data()`
- Called from: Analysis Service, API routes, main.py

**Conflict Resolution:** `on_conflict='site_id,URL,snapshot_date'` (allows historical snapshots)

---

### 3. **`gsc_keywords`** - Google Search Console Keyword Performance
**Purpose:** Detailed keyword-level performance data from Google Search Console.

| Column | Type | Nullable | Description |
|--------|------|----------|-------------|
| `id` | bigint | NO | Primary key, auto-increment |
| `site_id` | bigint | YES | Foreign key to sites table |
| `URL` | text | NO | Page URL that ranked for keyword |
| `Keyword` | text | NO | Search query/keyword |
| `Month` | text | NO | Time period (YYYY-MM format) |
| `Clicks` | integer | YES | Number of clicks from search results |
| `Impressions` | integer | YES | Number of times page appeared in search |
| `CTR` | numeric | YES | Click-through rate (clicks/impressions) |
| `Position` | numeric | YES | Average search result position |
| `keyword_hash` | text | YES | MD5 hash for deduplication |

**Data Source:**
- Google Search Console API via `src/core/google_apis.py`
- Saved via `src/database/supabase_client.py` - `save_gsc_keywords()`
- **Batch Processing:** 1000 records per batch to avoid timeouts
- Called from: Analysis Service, API routes, main.py

**Conflict Resolution:** `on_conflict='site_id,URL,Keyword,Month'`

---

### 4. **`gsc_traffic`** - Aggregated GSC Traffic by Page & Month
**Purpose:** Monthly traffic aggregations by URL for trend analysis and reporting.

| Column | Type | Nullable | Description |
|--------|------|----------|-------------|
| `id` | bigint | NO | Primary key, auto-increment |
| `site_id` | bigint | YES | Foreign key to sites table |
| `URL` | text | NO | Page URL |
| `Month` | text | NO | Time period (YYYY-MM format) |
| `Clicks` | integer | YES | Total clicks for URL in month |
| `Impressions` | integer | YES | Total impressions for URL in month |
| `CTR` | numeric | YES | Calculated CTR for month |
| `Position` | numeric | YES | Weighted average position for month |
| `traffic_hash` | text | YES | MD5 hash for deduplication |

**Data Source:**
- Aggregated from GSC keywords data in `main.py` and analysis service
- Saved via `src/database/supabase_client.py` - `save_gsc_traffic()`
- Called from: Analysis Service, API routes, main.py

**Conflict Resolution:** `on_conflict='site_id,URL,Month'`

---

### 5. **`ga_data`** - Google Analytics Page Performance
**Purpose:** Google Analytics metrics for page-level performance analysis.

| Column | Type | Nullable | Description |
|--------|------|----------|-------------|
| `id` | bigint | NO | Primary key, auto-increment |
| `site_id` | bigint | YES | Foreign key to sites table |
| `URL` | text | NO | Full page URL |
| `Month` | text | NO | Time period (YYYY-MM format) |
| `Google Analytics Page Views` | integer | YES | Total page views for month |
| `Active Users` | integer | YES | Number of active users for month |

**Data Source:**
- Google Analytics 4 API via `src/core/google_apis.py`
- Column mapping applied: `screenPageViews` → `Google Analytics Page Views`
- Saved via `src/database/supabase_client.py` - `save_ga_data()`
- **Note:** Only saves columns that exist in schema (filters out sessions, bounce rate, etc.)

**Conflict Resolution:** `on_conflict='site_id,URL,Month'`

---

### 6. **`internal_links`** - Internal Link Analysis
**Purpose:** Internal linking structure and anchor text analysis for SEO optimization.

| Column | Type | Nullable | Description |
|--------|------|----------|-------------|
| `id` | bigint | NO | Primary key, auto-increment |
| `site_id` | bigint | YES | Foreign key to sites table |
| `URL` | text | NO | Source page URL |
| `Target Hyperlink` | text | NO | Destination URL of the link |
| `Anchor Text` | text | YES | Link anchor text |
| `link_hash` | text | NO | MD5 hash of URL + Target for deduplication |
| `snapshot_date` | date | NO | Date when links were analyzed |

**Data Source:**
- **Primary:** WordPress API via `src/core/wordpress_api.py` (when available)
- **Fallback:** HTML parsing via `src/services/link_analysis_service.py`
- Saved via `src/database/supabase_client.py` - `save_internal_links()`

**Conflict Resolution:** `on_conflict='site_id,link_hash,snapshot_date'`

---

## 📊 **Computed Views**

### 1. **`latest_pages`** - Most Recent Page Data
**Purpose:** Always shows the most recent snapshot of each page for current analysis.

**SQL Definition:**
```sql
SELECT p.*, FROM pages p
JOIN (
  SELECT site_id, URL, max(snapshot_date) AS latest_date
  FROM pages GROUP BY site_id, URL
) latest ON p.site_id = latest.site_id 
  AND p.URL = latest.URL 
  AND p.snapshot_date = latest.latest_date
```

**Usage:** Dashboard displays, current page analysis, Excel report generation

---

### 2. **`monthly_traffic_trends`** - Domain-Level Traffic Trends
**Purpose:** Aggregated monthly traffic statistics by domain for trend analysis.

| Column | Type | Description |
|--------|------|-------------|
| `domain` | text | Website domain |
| `Month` | text | Time period (YYYY-MM) |
| `page_count` | bigint | Number of pages with traffic |
| `total_clicks` | bigint | Sum of all clicks for domain |
| `total_impressions` | bigint | Sum of all impressions for domain |
| `avg_ctr` | double precision | Average CTR across all pages |
| `weighted_position` | numeric | Click-weighted average position |

**Data Source:** Computed from `gsc_traffic` joined with `sites`

---

### 3. **`top_pages`** - Best Performing Pages
**Purpose:** Identifies highest-traffic pages across all metrics for optimization prioritization.

| Column | Type | Description |
|--------|------|-------------|
| `domain` | text | Website domain |
| `URL` | text | Page URL |
| `SEO Title` | text | Page title |
| `H1` | text | Main heading |
| `total_pageviews` | bigint | Sum of GA page views |
| `total_clicks` | bigint | Sum of GSC clicks |
| `total_impressions` | bigint | Sum of GSC impressions |

**Data Source:** Computed from `pages` + `gsc_traffic` + `ga_data` joined with `sites`
**Ordering:** Sorted by total clicks (DESC) for performance ranking

---

## 🔄 **Data Flow & Code Integration**

### **Analysis Pipeline:**
1. **Site Creation** → `sites` table via API
2. **Website Crawling** → `pages` table via crawler
3. **GSC Data Fetch** → `gsc_keywords` + `gsc_traffic` tables
4. **GA Data Fetch** → `ga_data` table  
5. **Internal Links Analysis** → `internal_links` table
6. **Report Generation** → Queries all tables + views

### **Key Code Files:**
- **`src/database/supabase_client.py`** - All database operations
- **`src/services/analysis_service.py`** - Orchestrates data collection & saving
- **`src/api/routes.py`** - API endpoints for analysis & reporting
- **`main_refactored.py`** - CLI entry point
- **`api_refactored.py`** - API server entry point

### **Conflict Resolution Strategy:**
All tables use `upsert()` with specific conflict keys to handle re-analysis:
- **Pages:** Allow multiple snapshots per URL
- **Keywords/Traffic/GA:** Update existing month data
- **Internal Links:** Track changes over time
- **Sites:** Prevent duplicate domains

This architecture enables **historical tracking**, **incremental updates**, and **efficient reporting** while maintaining data integrity across multiple analysis runs.

---

## 🔐 **Hash Columns & Deduplication**

### **Hash Generation Logic:**

#### **`pages.url_hash`**
```python
# Generated in save_pages_data()
url_hash = hashlib.md5(url.encode()).hexdigest()
```
**Purpose:** Prevents duplicate URLs, enables efficient lookups

#### **`gsc_keywords.keyword_hash`**
```python
# Generated in save_gsc_keywords()
keyword_hash = hashlib.md5(f"{keyword}{url}{month}".encode()).hexdigest()
```
**Purpose:** Unique identifier for keyword-URL-month combinations

#### **`gsc_traffic.traffic_hash`**
```python
# Generated in save_gsc_traffic()
traffic_hash = hashlib.md5(f"{url}{month}".encode()).hexdigest()
```
**Purpose:** Unique identifier for URL-month traffic records

#### **`internal_links.link_hash`**
```python
# Generated in save_internal_links()
link_hash = hashlib.md5(f"{source_url}|{target_url}".encode()).hexdigest()
```
**Purpose:** Prevents duplicate link records, tracks link changes over time

---

## 📈 **Data Processing & Aggregation**

### **GSC Keywords → Traffic Aggregation:**
The system processes raw keyword data into aggregated traffic metrics:

1. **Raw Keywords** (`gsc_keywords`) - Individual keyword performance
2. **Aggregated Traffic** (`gsc_traffic`) - Monthly totals per URL
3. **Computed Metrics:**
   - **Total Clicks:** `SUM(clicks)` per URL/month
   - **Total Impressions:** `SUM(impressions)` per URL/month
   - **Weighted Position:** `SUM(position * impressions) / SUM(impressions)`
   - **CTR:** `total_clicks / total_impressions`

### **Column Mapping (GA Data):**
Google Analytics API returns different column names than database schema:

| GA API Column | Database Column |
|---------------|-----------------|
| `pagePath` | `URL` |
| `screenPageViews` | `Google Analytics Page Views` |
| `activeUsers` | `Active Users` |
| ~~`sessions`~~ | *(Filtered out - not in schema)* |
| ~~`bounceRate`~~ | *(Filtered out - not in schema)* |
| ~~`averageSessionDuration`~~ | *(Filtered out - not in schema)* |

### **Batch Processing:**
Large datasets are processed in batches to prevent timeouts:
- **GSC Keywords:** 1000 records per batch
- **Other tables:** Single batch (smaller datasets)

---

## 🚨 **Important Notes**

### **Schema Limitations:**
- **GA Data:** Only supports page views and active users (not sessions, bounce rate, etc.)
- **Internal Links:** Simplified schema compared to WordPress API data
- **Pages:** Some computed columns (GSC Clicks, etc.) may be populated separately

### **Data Freshness:**
- **Pages:** Snapshot-based (historical tracking enabled)
- **Keywords/Traffic/GA:** Monthly aggregations (overwrites existing month data)
- **Internal Links:** Snapshot-based with change tracking

### **API Integration Points:**
- **WordPress API:** Provides rich internal link metadata when available
- **Google APIs:** Require service account credentials stored in `sites.service_account_data`
- **Fallback Systems:** HTML parsing when APIs unavailable

This comprehensive schema supports the full SEO analysis pipeline while maintaining flexibility for future enhancements and integrations.
