# Progressive Batch Pipeline Design

## 🎯 **Core Concept: Save Early, <PERSON>hance Later**

Instead of processing all 682 pages in memory, we can implement a **staged pipeline** that saves data progressively and enhances it in subsequent passes.

## 📊 **New Pipeline Architecture**

### **Current Approach (Memory Intensive):**
```
Crawl 682 pages → Load all in memory → Merge all GSC data → Merge all GA data → Save everything
                                    ↑
                            Memory bottleneck (300MB+)
```

### **New Approach (Progressive Batches):**
```
Stage 1: Crawl & Save Basic Pages (batches of 50)
Stage 2: Enhance with GSC Data (batches of 100) 
Stage 3: Enhance with GA Data (batches of 100)
Stage 4: Calculate Derived Fields (batches of 100)
```

## 🔄 **Detailed Progressive Pipeline**

### **Stage 1: Crawl & Save Basic Pages**
```python
async def crawl_and_save_basic_pages(self, urls: List[str], batch_size: int = 50):
    """Crawl pages and save basic content immediately"""
    
    for batch_start in range(0, len(urls), batch_size):
        batch_urls = urls[batch_start:batch_start + batch_size]
        
        # Crawl batch
        batch_results = await self.crawler.crawl_site(batch_urls, output_dir)
        
        # Convert to basic DataFrame (no GSC/GA data yet)
        basic_df = self._create_basic_pages_dataframe(batch_results)
        
        # Save immediately to database
        supabase_client.save_pages_data(basic_df, mode='basic')
        
        logger.info(f"Saved batch {batch_start//batch_size + 1}: {len(batch_results)} pages")
        
        # Clear memory
        del batch_results, basic_df
```

### **Stage 2: Enhance with GSC Data**
```python
async def enhance_with_gsc_data(self, keywords_df: pd.DataFrame, batch_size: int = 100):
    """Add GSC metrics to existing pages in batches"""
    
    # Get all page URLs from database
    page_urls = supabase_client.get_page_urls_for_site(site_id)
    
    for batch_start in range(0, len(page_urls), batch_size):
        batch_urls = page_urls[batch_start:batch_start + batch_size]
        
        # Filter GSC data for this batch
        batch_gsc_data = keywords_df[keywords_df['URL'].isin(batch_urls)]
        
        # Aggregate GSC data for this batch only
        gsc_aggregated = self._aggregate_gsc_batch(batch_gsc_data)
        
        # Update database with GSC metrics
        supabase_client.update_pages_gsc_metrics(gsc_aggregated)
        
        logger.info(f"Enhanced batch {batch_start//batch_size + 1} with GSC data")
        
        # Clear memory
        del batch_gsc_data, gsc_aggregated
```

### **Stage 3: Enhance with GA Data**
```python
async def enhance_with_ga_data(self, ga_df: pd.DataFrame, batch_size: int = 100):
    """Add GA metrics to existing pages in batches"""
    
    page_urls = supabase_client.get_page_urls_for_site(site_id)
    
    for batch_start in range(0, len(page_urls), batch_size):
        batch_urls = page_urls[batch_start:batch_start + batch_size]
        
        # Filter GA data for this batch
        batch_ga_data = ga_df[ga_df['URL'].isin(batch_urls)]
        
        # Aggregate GA data for this batch
        ga_aggregated = self._aggregate_ga_batch(batch_ga_data)
        
        # Update database with GA metrics
        supabase_client.update_pages_ga_metrics(ga_aggregated)
        
        logger.info(f"Enhanced batch {batch_start//batch_size + 1} with GA data")
```

### **Stage 4: Calculate Derived Fields**
```python
async def calculate_derived_fields(self, batch_size: int = 100):
    """Calculate CTR, content hashes, etc. in batches"""
    
    total_pages = supabase_client.get_pages_count_for_site(site_id)
    
    for offset in range(0, total_pages, batch_size):
        # Get batch of pages from database
        pages_batch = supabase_client.get_pages_batch(site_id, offset, batch_size)
        
        # Calculate derived fields
        for page in pages_batch:
            # Calculate CTR
            if page['GSC Impressions'] > 0:
                page['CTR'] = round(page['GSC Clicks'] / page['GSC Impressions'], 4)
            
            # Calculate content hash
            page['content_hash'] = self._calculate_content_hash(page)
            
            # Calculate title length
            page['Title Length'] = len(page.get('SEO Title', ''))
        
        # Update database with calculated fields
        supabase_client.update_pages_derived_fields(pages_batch)
        
        logger.info(f"Calculated derived fields for batch {offset//batch_size + 1}")
```

## 🛠️ **Implementation Strategy**

### **1. Database Schema Enhancements**

#### **Add Processing Status Column:**
```sql
ALTER TABLE pages ADD COLUMN processing_status TEXT DEFAULT 'basic';
-- Values: 'basic', 'gsc_enhanced', 'ga_enhanced', 'complete'
```

#### **Add Batch Processing Methods:**
```python
class SupabaseClient:
    def save_pages_data_basic(self, df: pd.DataFrame):
        """Save basic page content without GSC/GA data"""
        
    def update_pages_gsc_metrics(self, gsc_data: pd.DataFrame):
        """Update existing pages with GSC metrics"""
        
    def update_pages_ga_metrics(self, ga_data: pd.DataFrame):
        """Update existing pages with GA metrics"""
        
    def update_pages_derived_fields(self, pages: List[dict]):
        """Update calculated fields like CTR, content_hash"""
        
    def get_pages_batch(self, site_id: int, offset: int, limit: int):
        """Get a batch of pages for processing"""
```

### **2. Modified Analysis Service**

```python
async def run_progressive_analysis(self, config: Dict[str, Any], task_id: str):
    """Run analysis with progressive batch processing"""
    
    try:
        # Stage 1: Crawl and save basic pages (memory efficient)
        if progress_callback:
            progress_callback(task_id, 10, "Stage 1: Crawling and saving basic pages...")
        
        await self.crawl_and_save_basic_pages(website_urls, batch_size=50)
        
        # Stage 2: Fetch and process GSC data
        if progress_callback:
            progress_callback(task_id, 40, "Stage 2: Fetching GSC data...")
        
        keywords_df = fetch_gsc_data(...)  # Still fetch all at once (API limitation)
        
        if progress_callback:
            progress_callback(task_id, 50, "Stage 2: Enhancing pages with GSC data...")
        
        await self.enhance_with_gsc_data(keywords_df, batch_size=100)
        
        # Stage 3: Fetch and process GA data
        if progress_callback:
            progress_callback(task_id, 70, "Stage 3: Fetching GA data...")
        
        ga_df = fetch_ga_data(...)
        
        if progress_callback:
            progress_callback(task_id, 80, "Stage 3: Enhancing pages with GA data...")
        
        await self.enhance_with_ga_data(ga_df, batch_size=100)
        
        # Stage 4: Calculate derived fields
        if progress_callback:
            progress_callback(task_id, 90, "Stage 4: Calculating derived fields...")
        
        await self.calculate_derived_fields(batch_size=100)
        
        # Stage 5: Process other data types
        if progress_callback:
            progress_callback(task_id, 95, "Stage 5: Saving GSC keywords and internal links...")
        
        supabase_client.save_gsc_keywords(keywords_df)
        supabase_client.save_internal_links(internal_links_df)
        
        if progress_callback:
            progress_callback(task_id, 100, "Analysis completed!")
            
    except Exception as e:
        logger.exception(f"Error in progressive analysis: {e}")
        raise
```

## 📊 **Memory Usage Comparison**

### **Current Approach:**
```
Peak Memory Usage: ~300MB
- 682 pages × 50KB = 34MB content
- DataFrame overhead = 100MB
- GSC merge operations = 100MB
- GA merge operations = 50MB
- Record conversion = 50MB
```

### **Progressive Approach:**
```
Peak Memory Usage: ~25MB
- Stage 1: 50 pages × 50KB = 2.5MB per batch
- Stage 2: 100 URLs × GSC data = 10MB per batch
- Stage 3: 100 URLs × GA data = 5MB per batch
- Stage 4: 100 pages × calculations = 5MB per batch
```

**Memory Reduction: 92%** (300MB → 25MB)

## 🎯 **Benefits of Progressive Pipeline**

### **1. Memory Efficiency**
- **Constant memory usage** regardless of site size
- **No memory spikes** during processing
- **Garbage collection friendly** (small batches)

### **2. Progress Visibility**
- **Real-time progress** through each stage
- **Granular status updates** for users
- **Clear error isolation** if something fails

### **3. Fault Tolerance**
- **Resume capability** if process crashes
- **Partial results preserved** in database
- **Stage-by-stage validation** possible

### **4. Scalability**
- **Handles any site size** with same memory footprint
- **Configurable batch sizes** for different environments
- **Parallel processing potential** for different stages

## 🔧 **Implementation Priority**

### **Phase 1: Basic Progressive Pipeline**
1. Implement basic page saving (Stage 1)
2. Add GSC enhancement (Stage 2)
3. Add GA enhancement (Stage 3)

### **Phase 2: Advanced Features**
1. Add derived field calculations (Stage 4)
2. Implement resume capability
3. Add parallel processing for independent stages

### **Phase 3: Optimization**
1. Fine-tune batch sizes
2. Add caching for repeated operations
3. Implement smart dependency tracking

This approach maintains all the data richness and complexity while dramatically reducing memory usage and providing much better user experience through progress tracking.
