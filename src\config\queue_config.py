"""
Server-specific configuration for task queue limits
Generated based on system analysis
"""

# Task Queue Configuration
GLOBAL_CONCURRENT_LIMIT = 12
PER_USER_CONCURRENT_LIMIT = 5
MAX_QUEUE_SIZE = 500

# Resource Monitoring Thresholds
MEMORY_THRESHOLD_PERCENT = 90
CPU_THRESHOLD_PERCENT = 80

# Task Timeouts (seconds)
DEFAULT_TASK_TIMEOUT = 1800  # 30 minutes
MAX_TASK_TIMEOUT = 3600      # 1 hour

# Retry Configuration
DEFAULT_MAX_RETRIES = 3
RETRY_BASE_DELAY = 60        # 1 minute
MAX_RETRY_DELAY = 1800       # 30 minutes

# Queue Processing
QUEUE_WORKER_SLEEP = 1       # 1 second between queue checks
CLEANUP_INTERVAL = 300       # 5 minutes
