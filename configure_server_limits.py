#!/usr/bin/env python3
"""
Configure server limits based on your hardware capacity
"""
import psutil
import sys
import os

def analyze_server_capacity():
    """Analyze current server capacity and recommend limits"""
    print("🔍 Analyzing Server Capacity...")
    print("=" * 50)
    
    # Get system info
    cpu_count = psutil.cpu_count()
    memory = psutil.virtual_memory()
    memory_gb = memory.total / (1024**3)
    
    print(f"💻 System Information:")
    print(f"   CPU Cores: {cpu_count}")
    print(f"   Total Memory: {memory_gb:.1f} GB")
    print(f"   Available Memory: {memory.available / (1024**3):.1f} GB")
    print(f"   Current CPU Usage: {psutil.cpu_percent(interval=1):.1f}%")
    print(f"   Current Memory Usage: {memory.percent:.1f}%")
    
    return cpu_count, memory_gb

def recommend_limits(cpu_count, memory_gb):
    """Recommend safe limits based on server capacity"""
    print(f"\n🎯 Recommended Limits:")
    
    # Conservative recommendations
    if memory_gb < 2:
        global_limit = 2
        per_user_limit = 1
        queue_size = 20
        memory_threshold = 85
    elif memory_gb < 4:
        global_limit = 3
        per_user_limit = 2
        queue_size = 50
        memory_threshold = 85
    elif memory_gb < 8:
        global_limit = 5  # Current setting
        per_user_limit = 3  # Current setting
        queue_size = 100
        memory_threshold = 90
    elif memory_gb < 16:
        global_limit = 8
        per_user_limit = 4
        queue_size = 200
        memory_threshold = 90
    else:
        global_limit = 12
        per_user_limit = 5
        queue_size = 500
        memory_threshold = 90
    
    # Adjust based on CPU cores
    if cpu_count < 2:
        global_limit = min(global_limit, 2)
    elif cpu_count < 4:
        global_limit = min(global_limit, 4)
    
    print(f"   🌐 Global Concurrent Limit: {global_limit}")
    print(f"   👤 Per-User Limit: {per_user_limit}")
    print(f"   📋 Max Queue Size: {queue_size}")
    print(f"   🧠 Memory Threshold: {memory_threshold}%")
    print(f"   ⚡ CPU Threshold: 80%")
    
    return global_limit, per_user_limit, queue_size, memory_threshold

def generate_config_file(global_limit, per_user_limit, queue_size, memory_threshold):
    """Generate configuration file"""
    config_content = f'''"""
Server-specific configuration for task queue limits
Generated based on system analysis
"""

# Task Queue Configuration
GLOBAL_CONCURRENT_LIMIT = {global_limit}
PER_USER_CONCURRENT_LIMIT = {per_user_limit}
MAX_QUEUE_SIZE = {queue_size}

# Resource Monitoring Thresholds
MEMORY_THRESHOLD_PERCENT = {memory_threshold}
CPU_THRESHOLD_PERCENT = 80

# Task Timeouts (seconds)
DEFAULT_TASK_TIMEOUT = 1800  # 30 minutes
MAX_TASK_TIMEOUT = 3600      # 1 hour

# Retry Configuration
DEFAULT_MAX_RETRIES = 3
RETRY_BASE_DELAY = 60        # 1 minute
MAX_RETRY_DELAY = 1800       # 30 minutes

# Queue Processing
QUEUE_WORKER_SLEEP = 1       # 1 second between queue checks
CLEANUP_INTERVAL = 300       # 5 minutes
'''
    
    with open('src/config/queue_config.py', 'w') as f:
        f.write(config_content)
    
    print(f"\n📝 Configuration saved to: src/config/queue_config.py")

def show_monitoring_commands():
    """Show commands to monitor server load"""
    print(f"\n📊 Server Monitoring Commands:")
    print(f"   # Check current resource usage")
    print(f"   python -c \"import psutil; print(f'CPU: {{psutil.cpu_percent()}}%, Memory: {{psutil.virtual_memory().percent}}%')\"")
    print(f"   ")
    print(f"   # Monitor queue status")
    print(f"   curl -H 'Authorization: Bearer TOKEN' http://localhost:8000/queue/status")
    print(f"   ")
    print(f"   # Monitor resource status (admin only)")
    print(f"   curl -H 'Authorization: Bearer ADMIN_TOKEN' http://localhost:8000/queue/status/resources")

def show_scaling_options():
    """Show options for scaling"""
    print(f"\n🚀 Scaling Options:")
    print(f"   1. 📈 Vertical Scaling (Current Approach):")
    print(f"      - Increase server RAM/CPU")
    print(f"      - Adjust GLOBAL_CONCURRENT_LIMIT")
    print(f"      - Monitor resource usage")
    print(f"   ")
    print(f"   2. 📊 Horizontal Scaling (Future):")
    print(f"      - Multiple worker instances")
    print(f"      - Shared database queue")
    print(f"      - Load balancer")
    print(f"   ")
    print(f"   3. ☁️ Cloud Auto-Scaling:")
    print(f"      - AWS Lambda functions")
    print(f"      - Google Cloud Run")
    print(f"      - Azure Container Instances")

def main():
    """Analyze server and recommend configuration"""
    print("🔧 Server Capacity Analysis & Configuration")
    print("=" * 60)
    
    try:
        # Analyze current capacity
        cpu_count, memory_gb = analyze_server_capacity()
        
        # Get recommendations
        global_limit, per_user_limit, queue_size, memory_threshold = recommend_limits(cpu_count, memory_gb)
        
        # Generate config
        os.makedirs('src/config', exist_ok=True)
        generate_config_file(global_limit, per_user_limit, queue_size, memory_threshold)
        
        # Show monitoring info
        show_monitoring_commands()
        show_scaling_options()
        
        print(f"\n✅ Current Settings Analysis:")
        if global_limit == 5:
            print(f"   🎯 Current limits (5 global, 3 per-user) are OPTIMAL for your system")
        elif global_limit < 5:
            print(f"   ⚠️  Your system could benefit from LOWER limits ({global_limit} global)")
        else:
            print(f"   📈 Your system can handle HIGHER limits ({global_limit} global)")
        
        print(f"\n🛡️ Server Protection Status:")
        print(f"   ✅ Global concurrency limit prevents overload")
        print(f"   ✅ Per-user limits prevent single user monopolization")
        print(f"   ✅ Resource monitoring stops tasks if system stressed")
        print(f"   ✅ Queue size limits prevent memory exhaustion")
        print(f"   ✅ Priority system ensures important tasks run first")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing server: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n🎉 Server analysis complete!")
        print(f"💡 Your server is protected from overload with current settings.")
    else:
        print(f"\n❌ Server analysis failed")
