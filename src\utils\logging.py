"""
Logging configuration and utilities
"""
import logging
import sys
from typing import Optional

from src.config.settings import settings


def setup_logging(level: Optional[str] = None, format_str: Optional[str] = None) -> None:
    """Setup application logging configuration"""
    log_level = level or settings.log_level
    log_format = format_str or settings.log_format
    
    # Convert string level to logging constant
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Configure root logger with UTF-8 encoding
    logging.basicConfig(
        level=numeric_level,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('seo_analysis.log', mode='a', encoding='utf-8')
        ]
    )

    # Set console handler encoding to UTF-8 for Windows compatibility
    for handler in logging.getLogger().handlers:
        if isinstance(handler, logging.StreamHandler) and handler.stream == sys.stdout:
            # For Windows console, we need to handle Unicode properly
            if sys.platform.startswith('win'):
                try:
                    # Try to set UTF-8 encoding for console
                    import codecs
                    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'replace')
                except (AttributeError, ImportError):
                    # Fallback: remove Unicode characters from log messages
                    pass
    
    # Set specific loggers to appropriate levels
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('googleapiclient').setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance for the given name"""
    return logging.getLogger(name)


def safe_log_message(message: str) -> str:
    """
    Convert Unicode characters in log messages to ASCII-safe equivalents
    for Windows console compatibility
    """
    if sys.platform.startswith('win'):
        # Replace common Unicode emojis with text equivalents
        replacements = {
            '✅': '[SUCCESS]',
            '❌': '[ERROR]',
            '🔍': '[INFO]',
            '📁': '[FILE]',
            '📝': '[FORM]',
            '📖': '[READ]',
            '📄': '[CONTENT]',
            '🚀': '[START]',
            '📡': '[RESPONSE]',
            '🎯': '[TARGET]',
            '⚠️': '[WARNING]',
            '🔧': '[FIX]',
            '📋': '[LIST]',
            '🎉': '[COMPLETE]'
        }

        for unicode_char, replacement in replacements.items():
            message = message.replace(unicode_char, replacement)

    return message


# Initialize logging when module is imported
setup_logging()
