"""
Command-line interface for the SEO analysis tool
"""
import sys
import asyncio
import argparse
from typing import Optional

from src.services.analysis_service import SEOAnalysisService
from src.utils.file_utils import load_config_file
from src.utils.logging import get_logger
from src.config.settings import settings

logger = get_logger(__name__)


async def run_analysis_from_config(config_path: str) -> None:
    """Run analysis from a configuration file"""
    try:
        config = load_config_file(config_path)
        if not config:
            logger.error(f"Failed to load configuration from {config_path}")
            sys.exit(1)
        
        # Initialize analysis service
        analysis_service = SEOAnalysisService()
        
        # Run analysis
        result = await analysis_service.run_analysis(config, "cli-task")
        
        logger.info("Analysis completed successfully!")
        logger.info(f"Results: {result}")
        
    except Exception as e:
        logger.exception(f"Error running analysis from config file {config_path}")
        sys.exit(1)


def serve_api() -> None:
    """Start the API server"""
    import uvicorn
    from src.api.app import app
    
    logger.info(f"Starting API server on {settings.api_host}:{settings.api_port}")
    uvicorn.run(
        app,
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug
    )


def main() -> None:
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description="SEO Analysis Tool")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Serve command
    serve_parser = subparsers.add_parser("serve", help="Start the API server")
    serve_parser.add_argument("--host", default=settings.api_host, help="Host to bind to")
    serve_parser.add_argument("--port", type=int, default=settings.api_port, help="Port to bind to")
    serve_parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    
    # Analyze command
    analyze_parser = subparsers.add_parser("analyze", help="Run analysis from config file")
    analyze_parser.add_argument("config", help="Path to configuration JSON file")
    
    # Parse arguments
    args = parser.parse_args()
    
    if args.command == "serve":
        # Update settings if provided
        if hasattr(args, 'host') and args.host:
            settings.api_host = args.host
        if hasattr(args, 'port') and args.port:
            settings.api_port = args.port
        if hasattr(args, 'debug') and args.debug:
            settings.debug = args.debug
        
        serve_api()
    
    elif args.command == "analyze":
        asyncio.run(run_analysis_from_config(args.config))
    
    else:
        parser.print_help()
        sys.exit(1)


if __name__ == "__main__":
    main()
