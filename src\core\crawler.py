"""
Web crawling functionality
"""
import asyncio
import requests
import xml.etree.ElementTree as ET
from typing import List, Optional, Dict, Any
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from playwright.async_api import async_playwright

from src.config.settings import settings
from src.models.schemas import CrawlResult
from src.utils.text_processing import html_to_markdown
from src.utils.logging import get_logger

logger = get_logger(__name__)


class WebCrawler:
    """Web crawler for extracting content from websites"""
    
    def __init__(self, timeout: int = None):
        self.timeout = timeout
        # Headers to appear more like a regular browser
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        } or settings.crawl_timeout
        
    async def get_js_rendered_html(self, url: str) -> str:
        """Get HTML content using JavaScript rendering with Playwright"""
        async with async_playwright() as p:
            browser = await p.chromium.launch()
            page = await browser.new_page()
            await page.goto(url, timeout=settings.js_render_timeout)
            content = await page.content()
            await browser.close()
            return content
    
    def extract_internal_links(self, base_url: str, html: str) -> List[str]:
        """Extract all internal links from HTML content"""
        soup = BeautifulSoup(html, 'html.parser')
        domain = urlparse(base_url).netloc
        links = set()
        
        for a_tag in soup.find_all('a', href=True):
            href = a_tag['href']
            full_url = urljoin(base_url, href)
            parsed_url = urlparse(full_url)
            
            # Only include URLs from the same domain and exclude fragments
            if parsed_url.netloc == domain:
                # Remove fragments (#) from URLs
                clean_url = full_url.split('#')[0]
                # Remove trailing slash for consistency
                if clean_url.endswith('/') and clean_url != base_url + '/':
                    clean_url = clean_url[:-1]
                links.add(clean_url)
        
        return list(links)

    def discover_urls_from_sitemap(self, base_url: str) -> List[str]:
        """Discover URLs from XML sitemaps"""
        logger.info(f"Checking for sitemaps on {base_url}...")

        # Common sitemap locations to try
        sitemap_urls = [
            f"{base_url}/sitemap.xml",
            f"{base_url}/sitemap_index.xml",
            f"{base_url}/wp-sitemap.xml",
            f"{base_url}/sitemap-index.xml"
        ]

        # Also check robots.txt for sitemap references
        try:
            robots_url = f"{base_url}/robots.txt"
            response = requests.get(robots_url, timeout=self.timeout, headers=self.headers)
            if response.status_code == 200:
                for line in response.text.split('\n'):
                    if line.strip().lower().startswith('sitemap:'):
                        sitemap_url = line.split(':', 1)[1].strip()
                        if sitemap_url not in sitemap_urls:
                            sitemap_urls.append(sitemap_url)
                            logger.info(f"Found sitemap in robots.txt: {sitemap_url}")
        except Exception as e:
            logger.debug(f"Could not check robots.txt: {e}")

        all_urls = set()

        for sitemap_url in sitemap_urls:
            try:
                logger.info(f"Checking sitemap: {sitemap_url}")
                response = requests.get(sitemap_url, timeout=self.timeout, headers=self.headers)

                if response.status_code == 200:
                    logger.debug(f"Sitemap {sitemap_url} returned {len(response.content)} bytes")
                    urls = self._parse_sitemap_xml(response.content, base_url)
                    if urls:
                        logger.info(f"Found {len(urls)} URLs in {sitemap_url}")
                        all_urls.update(urls)
                    else:
                        logger.warning(f"Sitemap {sitemap_url} returned 0 URLs after parsing")
                else:
                    logger.debug(f"Sitemap {sitemap_url} returned status {response.status_code}")

            except Exception as e:
                logger.debug(f"Failed to fetch sitemap {sitemap_url}: {e}")
                continue

        result = list(all_urls)
        if result:
            logger.info(f"Total URLs discovered from sitemaps: {len(result)}")
        else:
            logger.info("No URLs found in sitemaps")

        return result

    def _parse_sitemap_xml(self, xml_content: bytes, base_url: str) -> List[str]:
        """Parse XML sitemap content and extract URLs"""
        try:
            root = ET.fromstring(xml_content)
            urls = []

            # Handle sitemap index (contains references to other sitemaps)
            if 'sitemapindex' in root.tag:
                logger.info("Found sitemap index, checking sub-sitemaps...")
                for sitemap in root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}sitemap'):
                    loc_elem = sitemap.find('{http://www.sitemaps.org/schemas/sitemap/0.9}loc')
                    if loc_elem is not None:
                        sub_sitemap_url = loc_elem.text
                        try:
                            response = requests.get(sub_sitemap_url, timeout=self.timeout, headers=self.headers)
                            if response.status_code == 200:
                                sub_urls = self._parse_sitemap_xml(response.content, base_url)
                                urls.extend(sub_urls)
                        except Exception as e:
                            logger.debug(f"Failed to fetch sub-sitemap {sub_sitemap_url}: {e}")

            # Handle regular sitemap (contains actual URLs)
            else:
                for url_elem in root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}url'):
                    loc_elem = url_elem.find('{http://www.sitemaps.org/schemas/sitemap/0.9}loc')
                    if loc_elem is not None:
                        url = loc_elem.text
                        # Only include URLs from the same domain
                        if url and urlparse(url).netloc == urlparse(base_url).netloc:
                            urls.append(url)

            return urls

        except ET.ParseError as e:
            logger.debug(f"Failed to parse sitemap XML: {e}")
            return []
        except Exception as e:
            logger.debug(f"Error processing sitemap: {e}")
            return []

    def crawl_url(self, url: str) -> tuple[Optional[CrawlResult], Optional[dict]]:
        """Crawl a single URL and extract content. Returns (result, error_info)"""
        try:
            response = requests.get(url, timeout=self.timeout, headers=self.headers)
            if response.status_code != 200:
                logger.warning(f"Skipping dead link: {url} (Status code: {response.status_code})")
                error_info = {
                    'type': 'http_error',
                    'status_code': response.status_code,
                    'message': f'HTTP {response.status_code}'
                }
                return None, error_info

            html_content = response.text
            soup = BeautifulSoup(html_content, 'html.parser')
            title = soup.title.string if soup.title else 'No title'
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            h1 = soup.find('h1')

            result = CrawlResult(
                url=url,
                title=title,
                description=meta_desc['content'] if meta_desc else '',
                h1=h1.text if h1 else '',
                text=html_to_markdown(html_content),
                raw_html=html_content
            )
            return result, None

        except requests.exceptions.Timeout:
            logger.warning(f"Timeout crawling {url}")
            error_info = {'type': 'timeout', 'message': 'Request timeout'}
            return None, error_info
        except requests.exceptions.ConnectionError:
            logger.warning(f"Connection error crawling {url}")
            error_info = {'type': 'connection_error', 'message': 'Connection failed'}
            return None, error_info
        except Exception as e:
            logger.error(f"Error crawling {url}: {e}")
            error_info = {'type': 'unknown_error', 'message': str(e)}
            return None, error_info

    def _should_try_js_fallback(self, url: str, error_info: dict) -> bool:
        """Determine if JavaScript fallback is worth trying based on the error type"""
        error_type = error_info.get('type')
        status_code = error_info.get('status_code')

        # Don't try JS fallback for server-side errors that JS can't fix
        if status_code:
            permanent_failures = [400, 401, 403, 404, 410, 451]  # Client/auth/permission errors
            server_failures = [500, 501, 502, 503, 504]  # Server errors

            if status_code in permanent_failures:
                logger.debug(f"Skipping JS fallback for {url} - permanent failure (HTTP {status_code})")
                return False
            elif status_code in server_failures:
                logger.debug(f"Skipping JS fallback for {url} - server error (HTTP {status_code})")
                return False

        # Try JS fallback for these scenarios:
        # - Timeout errors (site might be slow, JS might help)
        # - Connection errors (network issues, retry might work)
        # - Empty/minimal content (might be JS-rendered)
        # - Parsing errors (malformed HTML, JS might clean it up)

        logger.debug(f"JS fallback recommended for {url} - {error_type or 'unknown error'}")
        return True
    
    async def crawl_url_with_js(self, url: str) -> Optional[CrawlResult]:
        """Crawl a URL using JavaScript rendering as fallback"""
        try:
            logger.info(f"Trying JS rendering for {url}...")
            html = await self.get_js_rendered_html(url)
            soup = BeautifulSoup(html, 'html.parser')
            
            text = html_to_markdown(html)
            
            return CrawlResult(
                url=url,
                title=soup.title.string if soup.title else 'No title',
                description=soup.find('meta', attrs={'name': 'description'})['content'] 
                           if soup.find('meta', attrs={'name': 'description'}) else '',
                h1=soup.find('h1').text if soup.find('h1') else '',
                text=text,
                raw_html=html
            )
        except Exception as e:
            logger.error(f"JS rendering failed for {url}: {e}")
            return None
    
    async def crawl_site(self, urls: List[str], output_dir: str, batch_size: int = 50) -> List[CrawlResult]:
        """
        Crawl multiple URLs and return results with memory-efficient batch processing

        Args:
            urls: List of URLs to crawl
            output_dir: Directory to save results and failed URLs
            batch_size: Number of URLs to process in each batch (default: 50)
        """
        import os

        all_results = []
        failed_urls = []
        total_urls = len(urls)

        logger.info(f"Starting crawl of {total_urls} URLs in batches of {batch_size}")

        # Process URLs in batches to manage memory usage
        for batch_start in range(0, total_urls, batch_size):
            batch_end = min(batch_start + batch_size, total_urls)
            batch_urls = urls[batch_start:batch_end]
            batch_num = (batch_start // batch_size) + 1
            total_batches = (total_urls + batch_size - 1) // batch_size

            logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch_urls)} URLs)")

            batch_results = []

            for i, url in enumerate(batch_urls):
                current_url_num = batch_start + i + 1
                logger.info(f"Crawling URL {current_url_num}/{total_urls}: {url}")

                data, error_info = self.crawl_url(url)
                if not data:
                    # Smart fallback: only try JavaScript rendering when it makes sense
                    if self._should_try_js_fallback(url, error_info or {}):
                        logger.info(f"Trying JS rendering fallback for {url}...")
                        data = await self.crawl_url_with_js(url)
                        if not data:
                            failed_urls.append(url)
                            continue
                    else:
                        # Skip JS fallback for permanent failures
                        failed_urls.append(url)
                        continue

                batch_results.append(data)

            # Add batch results to main results
            all_results.extend(batch_results)

            # Log batch completion and memory info
            logger.info(f"Batch {batch_num} completed: {len(batch_results)} successful, {len(failed_urls)} failed so far")

            # Optional: Save intermediate results to prevent data loss
            if batch_num % 5 == 0:  # Save every 5 batches
                logger.info(f"Saving intermediate results after batch {batch_num}")
                self._save_intermediate_results(all_results, output_dir, batch_num)

        # Save failed URLs for debugging
        if failed_urls:
            failed_urls_path = os.path.join(output_dir, 'failed_urls.txt')
            with open(failed_urls_path, 'w') as f:
                for url in failed_urls:
                    f.write(url + '\n')
            logger.info(f"Saved {len(failed_urls)} failed URLs to {failed_urls_path}")

        logger.info(f"Crawling completed: {len(all_results)} successful, {len(failed_urls)} failed")
        return all_results

    def _save_intermediate_results(self, results: List[CrawlResult], output_dir: str, batch_num: int):
        """Save intermediate crawl results to prevent data loss"""
        import os
        import json

        try:
            intermediate_file = os.path.join(output_dir, f'intermediate_results_batch_{batch_num}.json')

            # Convert CrawlResult objects to dictionaries for JSON serialization
            results_data = []
            for result in results:
                if hasattr(result, 'model_dump'):
                    results_data.append(result.model_dump())
                elif hasattr(result, 'dict'):
                    results_data.append(result.dict())
                else:
                    results_data.append(result.__dict__)

            with open(intermediate_file, 'w', encoding='utf-8') as f:
                json.dump(results_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Saved {len(results)} intermediate results to {intermediate_file}")

        except Exception as e:
            logger.warning(f"Failed to save intermediate results: {e}")
    
    async def discover_urls_from_homepage(self, homepage: str) -> List[str]:
        """Discover all internal URLs using comprehensive strategy: sitemaps first, then homepage links, then fallbacks"""
        logger.info(f"Discovering URLs from {homepage}...")

        # Extract base URL for sitemap discovery
        parsed_url = urlparse(homepage)
        base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"

        # Method 1: Try sitemaps first (most comprehensive)
        try:
            logger.info("Method 1: Checking sitemaps...")
            sitemap_urls = self.discover_urls_from_sitemap(base_url)

            if sitemap_urls and len(sitemap_urls) > 1:
                # Add homepage if not already included
                if homepage not in sitemap_urls and base_url not in sitemap_urls:
                    sitemap_urls.insert(0, homepage)

                logger.info(f"Sitemap discovery successful: {len(sitemap_urls)} URLs found")
                return sitemap_urls
            else:
                logger.info("Sitemaps found limited URLs, trying homepage link extraction...")

        except Exception as e:
            logger.warning(f"Sitemap discovery failed: {e}, trying homepage link extraction...")

        # Method 2: Try BeautifulSoup homepage link extraction
        try:
            logger.info("Method 2: Extracting links from homepage with BeautifulSoup...")
            response = requests.get(homepage, timeout=self.timeout, headers=self.headers)
            if response.status_code == 200:
                html = response.text
                links = self.extract_internal_links(homepage, html)

                # Add the homepage itself and remove duplicates
                all_urls = [homepage] + links
                unique_urls = list(dict.fromkeys(all_urls))  # Remove duplicates while preserving order

                logger.info(f"BeautifulSoup discovered {len(unique_urls)} URLs from homepage")

                # If we found a reasonable number of links, use this result
                if len(unique_urls) > 1:  # More than just the homepage
                    return unique_urls
                else:
                    logger.info("BeautifulSoup found limited URLs, trying Playwright as fallback...")
            else:
                logger.warning(f"BeautifulSoup failed with status {response.status_code}, trying Playwright...")

        except Exception as e:
            logger.warning(f"BeautifulSoup failed: {e}, trying Playwright as fallback...")

        # Method 3: Fallback to Playwright for JavaScript-heavy sites
        try:
            logger.info("Method 3: Trying Playwright for JavaScript-rendered content...")
            html = await self.get_js_rendered_html(homepage)

            # Extract all internal links
            links = self.extract_internal_links(homepage, html)

            # Add the homepage itself and remove duplicates
            all_urls = [homepage] + links
            unique_urls = list(dict.fromkeys(all_urls))  # Remove duplicates while preserving order

            logger.info(f"Playwright discovered {len(unique_urls)} URLs")
            return unique_urls

        except Exception as e:
            logger.error(f"All URL discovery methods failed: {e}")
            logger.info("Falling back to homepage only")
            return [homepage]
