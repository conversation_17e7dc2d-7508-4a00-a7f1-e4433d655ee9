"""
Internal link analysis service
"""
import re
import pandas as pd
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup

from src.models.schemas import CrawlResult
from src.utils.text_processing import calculate_text_similarity
from src.utils.logging import get_logger

logger = get_logger(__name__)


class LinkAnalysisService:
    """Service for analyzing internal links and their relationships"""
    
    def __init__(self):
        # Define exclusion lists similar to WordPress plugin
        self.excluded_anchor_texts = [
            'Read More', 'Continue Reading', 'Learn More', 'View More', 
            'Get in touch', 'Click here', 'Read now', 'Contact us'
        ]
        
        self.excluded_url_patterns = [
            '/blog/', '/news/', '/page/', '/tag/', '/category/', 
            '/author/', '/archive/', '/feed/', '/rss/', 
            '/wp-admin/', '/wp-content/', '/wp-includes/'
        ]
    
    def calculate_relevance_score(self, source_title: str, target_title: str, 
                                anchor_text: str, source_url: str, target_url: str) -> float:
        """Calculate relevance score between source and target pages"""
        try:
            # Basic relevance calculation based on text similarity
            title_similarity = calculate_text_similarity(source_title, target_title)
            anchor_similarity = calculate_text_similarity(anchor_text, target_title)
            
            # Weight the similarities
            relevance_score = (title_similarity * 0.4) + (anchor_similarity * 0.6)
            
            # Bonus for exact keyword matches
            if anchor_text.lower() in target_title.lower():
                relevance_score += 0.2
            
            # Penalty for generic anchor text
            if anchor_text.lower() in [text.lower() for text in self.excluded_anchor_texts]:
                relevance_score *= 0.5
            
            return min(relevance_score, 1.0)  # Cap at 1.0
            
        except Exception as e:
            logger.warning(f"Error calculating relevance score: {e}")
            return 0.0
    
    def build_internal_links_sheet(self, crawl_results: List[CrawlResult], 
                                 data_df: pd.DataFrame, wp_data: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """Build internal links analysis from crawl results"""
        logger.info("Building internal links data...")
        
        # If we have WordPress API data with links, use it directly
        logger.debug(f"WordPress data received: {wp_data is not None}")
        if wp_data:
            logger.debug(f"WordPress data keys: {list(wp_data.keys()) if isinstance(wp_data, dict) else 'Not a dict'}")
            if 'internal_links_data' in wp_data:
                logger.debug(f"Internal links data found: {len(wp_data['internal_links_data'])} links")
            else:
                logger.debug("No 'internal_links_data' key found in WordPress data")

        if wp_data and 'internal_links_data' in wp_data and wp_data['internal_links_data']:
            logger.info(f"Using {len(wp_data['internal_links_data'])} internal links from WordPress API")
            
            # Transform the API data format to match our expected output format
            internal_links_rows = []
            for link in wp_data['internal_links_data']:
                internal_links_rows.append({
                    'URL': link.get('source_page_link', ''),
                    'Target Hyperlink': link.get('target_hyperlink', ''),
                    'Anchor Text': link.get('anchor_text', ''),
                    'Link Type': link.get('link_type', 'Unknown'),
                    'URL Topic': '',
                    'Relevance Score': link.get('relevance_score', None),
                    'Target Title': link.get('target_page_title_if_internal', '')
                })
            
            return pd.DataFrame(internal_links_rows)
        
        # Otherwise, fall back to the enhanced HTML parsing method
        logger.info("No internal links data from API, falling back to enhanced HTML parsing")
        
        internal_links_rows = []
        site_domain = None
        
        for page in crawl_results:
            src_url = page.url
            
            # Set site domain if not already set
            if not site_domain:
                site_domain = urlparse(src_url).netloc
            
            html = page.raw_html
            
            if not html:
                logger.warning(f"No HTML content for {src_url}, skipping link analysis")
                continue
            
            # Clean HTML similar to WordPress plugin
            soup = BeautifulSoup(html, 'html.parser')
            
            # Remove navigation elements
            for nav in soup.find_all(['nav', 'header', 'footer']):
                nav.decompose()
            
            # Remove elements with specific classes/IDs that are typically navigation
            for element in soup.find_all(attrs={'class': re.compile(r'(nav|menu|sidebar|widget)', re.I)}):
                element.decompose()
            
            for element in soup.find_all(attrs={'id': re.compile(r'(nav|menu|sidebar|widget)', re.I)}):
                element.decompose()
            
            # Process remaining links
            for a_tag in soup.find_all('a', href=True):
                href = a_tag['href']
                anchor_text = a_tag.get_text().strip()
                
                # Skip empty anchors
                if not anchor_text:
                    continue
                    
                # Skip excluded anchor texts
                if any(excluded.lower() in anchor_text.lower() for excluded in self.excluded_anchor_texts):
                    continue
                
                # Skip special protocols
                if re.match(r'^(mailto|tel|sms|javascript):', href):
                    continue
                    
                # Skip TOC links
                if re.search(r'#(toc|tableofcontents|contents)', href, re.IGNORECASE):
                    continue
                
                # Normalize URL
                full_url = urljoin(src_url, href)
                parsed_url = urlparse(full_url)
                
                # Skip excluded URL patterns
                if any(pattern in parsed_url.path for pattern in self.excluded_url_patterns):
                    continue
                
                # Determine link type
                link_type = 'External'
                target_title = ''
                relevance_score = None
                
                if parsed_url.netloc == site_domain:
                    if '#' in href:
                        link_type = 'Jump Link'
                    else:
                        link_type = 'Internal'
                        
                        # Try to find target title for internal links
                        target_url_no_hash = full_url.split('#')[0]
                        for target_page in crawl_results:
                            if target_page.url == target_url_no_hash:
                                target_title = target_page.title
                                
                                # Calculate relevance score
                                relevance_score = self.calculate_relevance_score(
                                    page.title, target_title, anchor_text, src_url, target_url_no_hash
                                )
                                break
                
                # Get topic if available
                topic = ''
                if isinstance(data_df, pd.DataFrame) and 'Topic' in data_df.columns and 'URL' in data_df.columns:
                    matching_rows = data_df[data_df['URL'] == src_url]
                    if not matching_rows.empty:
                        topic = matching_rows['Topic'].iloc[0]
                
                internal_links_rows.append({
                    'URL': src_url,
                    'Target Hyperlink': full_url.split('#')[0],  # Remove fragment
                    'Anchor Text': anchor_text,
                    'Link Type': link_type,
                    'URL Topic': topic,
                    'Target Title': target_title,
                    'Relevance Score': relevance_score
                })
        
        return pd.DataFrame(internal_links_rows)
