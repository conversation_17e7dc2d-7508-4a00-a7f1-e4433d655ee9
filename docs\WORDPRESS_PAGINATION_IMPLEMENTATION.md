# WordPress API Pagination Implementation

## Overview

This implementation adds pagination support to both the WordPress plugin and Python client to handle sites with large numbers of pages efficiently. This prevents memory exhaustion, timeout issues, and improves performance for sites with hundreds or thousands of pages.

## WordPress Plugin Changes

### New API Endpoints

1. **Enhanced `/data` endpoint** with pagination and date filtering parameters:
   - `page` (default: 1) - Page number to fetch
   - `per_page` (default: 50, max: 200) - Number of items per page
   - `data_type` (default: 'all') - Type of data to fetch ('all', 'pages', 'links', 'meta')
   - `modified_since` (optional) - Only fetch content modified after this date (YYYY-MM-DD format)
   - `modified_until` (optional) - Only fetch content modified before this date (YYYY-MM-DD format)

2. **New `/data/counts` endpoint** for getting total counts:
   - Returns total page counts and site metadata
   - Used by Python client to determine pagination strategy

### Updated Functions

- `get_publish_seo_data($page, $per_page, $modified_since, $modified_until)` - Now supports pagination and date filtering
- `get_internal_links_data_for_site($page, $per_page, $modified_since, $modified_until)` - Now supports pagination and date filtering
- `get_total_counts($modified_since, $modified_until)` - New function to return site statistics with optional date filtering

### Database Query Optimization

- Added `LIMIT` and `OFFSET` clauses to SQL queries
- Added `ORDER BY post_modified DESC` for consistent pagination (changed from post_date for better incremental support)
- Added date range filtering with `WHERE post_modified >= ? AND post_modified <= ?`
- Maintained AIOSEO noindex filtering with pagination and date ranges

## Python Client Changes

### Enhanced WordPressAPIClient

1. **Smart Pagination Strategy**:
   - Automatically detects total pages using `/data/counts` endpoint
   - Calculates optimal batch size based on site size
   - Uses adaptive pagination (50-100 pages per request)

2. **New Methods**:
   - `fetch_counts(wp_api_url, modified_since, modified_until)` - Gets total page counts with optional date filtering
   - `_fetch_single_page()` - Fetches a single page of data with date range support
   - Enhanced `fetch_data(wp_api_url, modified_since, modified_until)` - Orchestrates paginated fetching with smart date filtering

3. **Fallback Handling**:
   - Falls back to single request if counts endpoint fails
   - Continues with remaining pages if individual requests fail
   - Maintains backward compatibility with non-paginated plugins

## Usage Examples

### WordPress Plugin API

```bash
# Get total counts
curl -H "X-Plugin-API-Key: your-key" \
  "https://yoursite.com/wp-json/wp-data-exporter/v1/data/counts"

# Get first page of data (50 items)
curl -H "X-Plugin-API-Key: your-key" \
  "https://yoursite.com/wp-json/wp-data-exporter/v1/data?page=1&per_page=50"

# Get only pages data (no links)
curl -H "X-Plugin-API-Key: your-key" \
  "https://yoursite.com/wp-json/wp-data-exporter/v1/data?data_type=pages"

# Get pages modified in the last 30 days
curl -H "X-Plugin-API-Key: your-key" \
  "https://yoursite.com/wp-json/wp-data-exporter/v1/data?modified_since=2024-01-01&modified_until=2024-01-31"

# Get counts for a specific date range
curl -H "X-Plugin-API-Key: your-key" \
  "https://yoursite.com/wp-json/wp-data-exporter/v1/data/counts?modified_since=2024-01-01"
```

### Python Client

```python
from src.core.wordpress import WordPressAPIClient

# Initialize client
wp_client = WordPressAPIClient('your-api-key')

# Detect API endpoint
wp_api_url = wp_client.detect_wp_api('https://yoursite.com')

# Fetch all data with automatic pagination
data = wp_client.fetch_data(wp_api_url)

# Fetch data with date range filtering (for incremental analysis)
from datetime import datetime, timedelta
thirty_days_ago = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
today = datetime.now().strftime('%Y-%m-%d')

incremental_data = wp_client.fetch_data(wp_api_url, thirty_days_ago, today)

# Access paginated results
pages = data['publish_seo_data']  # All pages from all requests
links = data['internal_links_data']  # All links from all requests
date_range = data.get('date_range', {})  # Applied date range info
```

## Performance Benefits

### Before (No Pagination)
- ❌ Loads ALL pages into memory at once
- ❌ Single large database query
- ❌ Risk of PHP memory exhaustion (>500 pages)
- ❌ Risk of execution timeouts (>1000 pages)
- ❌ Large JSON responses (>10MB for big sites)

### After (With Pagination)
- ✅ Loads pages in manageable batches
- ✅ Multiple smaller database queries
- ✅ Controlled memory usage
- ✅ Reduced execution time per request
- ✅ Smaller JSON responses per request
- ✅ Progress tracking and error recovery
- ✅ Smart incremental analysis (only fetch recently modified content)
- ✅ Consistent date-based filtering across WordPress and Google APIs

## Smart Incremental Analysis

### How It Works

The implementation now supports smart incremental analysis that automatically coordinates date ranges between WordPress content and Google APIs:

1. **Date Range Calculation**: The Python client calculates the optimal date range based on the last analysis date stored in Supabase
2. **WordPress Filtering**: WordPress API filters content to only pages modified within the calculated date range
3. **Google API Alignment**: Google Search Console and Analytics use the same date range for consistency
4. **Efficient Processing**: Only recently changed content is processed, dramatically reducing analysis time

### Benefits for Large Sites

- **Faster Analysis**: Only process content that has actually changed
- **Reduced API Calls**: Fewer requests to WordPress, Google APIs, and database
- **Lower Resource Usage**: Less memory and processing time required
- **Consistent Data**: All data sources use the same date range for accurate reporting

### Example Workflow

```python
# Incremental analysis automatically uses smart date ranges
config = {
    'incremental': True,  # Enable smart incremental mode
    'wp_api_key': 'your-key',
    # ... other config
}

# The system will:
# 1. Check last analysis date from Supabase
# 2. Calculate optimal date range (e.g., last 7 days)
# 3. Fetch only WordPress content modified in that range
# 4. Fetch Google data for the same date range
# 5. Process and save only the incremental changes
```

## Configuration

### WordPress Plugin Settings

The plugin automatically handles pagination with these defaults:
- Default page size: 50 items
- Maximum page size: 200 items
- Automatic ordering by post date (newest first)

### Python Client Settings

The client automatically optimizes pagination:
- Adaptive batch size: 50-100 items based on site size
- Automatic retry on failed requests
- Progress logging for large sites

## Testing

Use the provided test script to verify pagination:

```bash
python test_wordpress_pagination.py
```

Update the test configuration with your WordPress site details:
- WordPress site URL
- WordPress plugin API key

## Backward Compatibility

The implementation maintains full backward compatibility:
- Existing API calls without pagination parameters work unchanged
- Python client falls back to single requests if pagination fails
- Non-paginated WordPress plugins continue to work

## Troubleshooting

### Common Issues

1. **Memory errors on large sites**:
   - Reduce `per_page` parameter in API calls
   - Check PHP memory limit in WordPress

2. **Timeout errors**:
   - Increase PHP execution time limit
   - Use smaller batch sizes

3. **Missing data**:
   - Check WordPress plugin API key
   - Verify plugin is activated
   - Check WordPress error logs

### Debug Logging

Enable debug logging to monitor pagination:

```python
import logging
logging.getLogger('src.core.wordpress').setLevel(logging.DEBUG)
```

## Performance Recommendations

### Small Sites (<100 pages)
- Use default settings
- Single request usually sufficient

### Medium Sites (100-500 pages)
- Use 50-100 items per page
- Monitor memory usage

### Large Sites (500+ pages)
- Use 50 items per page
- Consider incremental analysis
- Monitor execution time

### Very Large Sites (1000+ pages)
- Use 25-50 items per page
- Implement progress tracking
- Consider background processing
