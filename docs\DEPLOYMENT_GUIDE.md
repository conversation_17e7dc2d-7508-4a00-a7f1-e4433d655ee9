# SEO Analysis Tool - Render.com Deployment Guide

## 🚀 **Quick Deployment Steps**

### **Step 1: Push to GitHub**
```bash
# Initialize git repository (if not already done)
git init
git add .
git commit -m "Initial commit for deployment"

# Push to GitHub
git remote add origin https://github.com/YOUR_USERNAME/YOUR_REPO_NAME.git
git branch -M main
git push -u origin main
```

### **Step 2: Deploy on Render.com**

1. **Go to [render.com](https://render.com)** and sign up/login
2. **Click "New +"** → **"Web Service"**
3. **Connect GitHub** and select your repository
4. **Configure the service:**
   - **Name**: `seo-analyzer` (or your preferred name)
   - **Environment**: `Python 3`
   - **Build Command**: `pip install -r requirements.txt`
   - **Start Command**: `uvicorn api_refactored:app --host 0.0.0.0 --port $PORT`
   - **Plan**: `Free` (for testing)

### **Step 3: Set Environment Variables**

In Render dashboard, go to **Environment** tab and add:

```
SUPABASE_URL=https://ltrymguxcxzyofxmbutv.supabase.co
SUPABASE_KEY=your_supabase_anon_key
API_HOST=0.0.0.0
PYTHON_VERSION=3.11.0
```

### **Step 4: Deploy**
- Click **"Create Web Service"**
- Render will automatically build and deploy your app
- You'll get a URL like: `https://your-app-name.onrender.com`

---

## 🔧 **Configuration Details**

### **Files Created for Deployment:**
- ✅ `render.yaml` - Render configuration
- ✅ `Dockerfile` - Container configuration (alternative)
- ✅ `start.sh` - Startup script
- ✅ `.gitignore` - Exclude unnecessary files
- ✅ Updated CORS settings for Render domains

### **Environment Variables Required:**
| Variable | Value | Description |
|----------|-------|-------------|
| `SUPABASE_URL` | Your Supabase project URL | Database connection |
| `SUPABASE_KEY` | Your Supabase anon key | Database authentication |
| `API_HOST` | `0.0.0.0` | Allow external connections |
| `PYTHON_VERSION` | `3.11.0` | Python version |

### **Render Service Configuration:**
- **Runtime**: Python 3.11
- **Build**: Automatic from requirements.txt
- **Start**: FastAPI with Uvicorn
- **Port**: Auto-assigned by Render
- **Plan**: Free tier (750 hours/month)

---

## 🎯 **Post-Deployment Steps**

### **1. Test Your Deployment**
Visit your Render URL and verify:
- ✅ Homepage loads correctly
- ✅ Site management works
- ✅ Excel report generation works
- ✅ All API endpoints respond

### **2. Update Supabase CORS**
In your Supabase dashboard:
1. Go to **Settings** → **API**
2. Add your Render URL to **CORS origins**:
   ```
   https://your-app-name.onrender.com
   ```

### **3. Custom Domain (Optional)**
In Render dashboard:
1. Go to **Settings** → **Custom Domains**
2. Add your domain (e.g., `seo-analyzer.yourdomain.com`)
3. Update DNS records as instructed

---

## 📊 **Monitoring & Maintenance**

### **Render Dashboard Features:**
- **Logs**: Real-time application logs
- **Metrics**: CPU, memory, response times
- **Deployments**: History and rollback options
- **Environment**: Manage environment variables

### **Automatic Deployments:**
- Render auto-deploys on every git push to main branch
- Build logs show deployment progress
- Zero-downtime deployments

### **Free Tier Limitations:**
- **750 hours/month** (about 31 days)
- **Sleeps after 15 minutes** of inactivity
- **Cold start delay** (~30 seconds to wake up)
- **512MB RAM** limit

---

## 🔍 **Troubleshooting**

### **Common Issues:**

#### **Build Fails:**
- Check `requirements.txt` for correct package versions
- Review build logs in Render dashboard
- Ensure Python version compatibility

#### **App Won't Start:**
- Verify start command: `uvicorn api_refactored:app --host 0.0.0.0 --port $PORT`
- Check environment variables are set
- Review application logs

#### **Database Connection Issues:**
- Verify `SUPABASE_URL` and `SUPABASE_KEY`
- Check Supabase CORS settings
- Ensure Supabase project is active

#### **Static Files Not Loading:**
- Verify `public/` directory is in repository
- Check static file mounting in `api_refactored.py`
- Review file paths and permissions

### **Debug Commands:**
```bash
# Local testing before deployment
uvicorn api_refactored:app --host 0.0.0.0 --port 8000 --reload

# Check requirements
pip freeze > requirements.txt

# Test Docker build (optional)
docker build -t seo-analyzer .
docker run -p 8000:8000 seo-analyzer
```

---

## 🎉 **Success Checklist**

After deployment, verify these features work:

- [ ] **Homepage**: Dashboard loads with site list
- [ ] **Site Management**: Add, edit, delete sites
- [ ] **Analysis**: Run SEO analysis on sites
- [ ] **Excel Reports**: Generate and download reports
- [ ] **API Endpoints**: All REST endpoints respond
- [ ] **Database**: Data persists correctly
- [ ] **File Uploads**: Service account uploads work
- [ ] **Error Handling**: Graceful error responses

---

## 📞 **Support**

### **Render Support:**
- [Render Documentation](https://render.com/docs)
- [Render Community](https://community.render.com)
- [Render Status](https://status.render.com)

### **Application Logs:**
- Check Render dashboard logs for errors
- Monitor Supabase logs for database issues
- Use browser dev tools for frontend debugging

---

**🎯 Your SEO Analysis Tool will be live at: `https://your-app-name.onrender.com`**

**Next Steps:**
1. Push your code to GitHub
2. Connect to Render
3. Set environment variables
4. Deploy and test!
