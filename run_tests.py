#!/usr/bin/env python3
"""
Test Runner Script

Provides convenient commands for running different types of tests.
"""

import sys
import subprocess
from pathlib import Path

def run_command(cmd):
    """Run a command and return the result"""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    print(result.stdout)
    if result.stderr:
        print(result.stderr)
    return result.returncode == 0

def main():
    if len(sys.argv) < 2:
        print("Usage: python run_tests.py [command]")
        print("Commands:")
        print("  all        - Run all tests")
        print("  unit       - Run unit tests only")
        print("  integration - Run integration tests only") 
        print("  functional - Run functional tests only")
        print("  coverage   - Run tests with coverage report")
        print("  fast       - Run tests excluding slow ones")
        print("  debug      - Run debug scripts")
        return
    
    command = sys.argv[1]
    
    if command == "all":
        success = run_command(["python", "-m", "pytest", "tests/"])
    elif command == "unit":
        success = run_command(["python", "-m", "pytest", "tests/unit/"])
    elif command == "integration":
        success = run_command(["python", "-m", "pytest", "tests/integration/"])
    elif command == "functional":
        success = run_command(["python", "-m", "pytest", "tests/functional/"])
    elif command == "coverage":
        success = run_command(["python", "-m", "pytest", "tests/", "--cov=src", "--cov-report=html"])
    elif command == "fast":
        success = run_command(["python", "-m", "pytest", "tests/", "-m", "not slow"])
    elif command == "debug":
        print("Available debug scripts:")
        debug_dir = Path("tests/debug")
        if debug_dir.exists():
            for script in debug_dir.glob("*.py"):
                print(f"  python {script}")
        success = True
    else:
        print(f"Unknown command: {command}")
        success = False
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
