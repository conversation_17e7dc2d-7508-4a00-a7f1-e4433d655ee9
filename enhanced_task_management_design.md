# Enhanced Task Management System Design

## Overview
This document outlines a comprehensive task management system that addresses all current limitations:
- Database-persistent task tracking
- User authentication and authorization
- Task queuing and resource management
- Enhanced frontend task management
- Task-specific logging

## 1. Database Schema for Task Management

### Tasks Table
```sql
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id),
    site_id INTEGER REFERENCES sites(site_id),
    task_type VARCHAR(50) NOT NULL, -- 'analysis', 'report_generation', 'reanalysis'
    status VARCHAR(20) NOT NULL DEFAULT 'queued', -- 'queued', 'running', 'completed', 'failed', 'cancelled'
    priority INTEGER DEFAULT 5, -- 1-10, lower = higher priority
    progress INTEGER DEFAULT 0, -- 0-100
    message TEXT,
    config JSONB, -- Task configuration
    result JSONB, -- Task results
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    estimated_duration INTEGER, -- seconds
    actual_duration INTEGER, -- seconds
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3
);

CREATE INDEX idx_tasks_user_id ON tasks(user_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_created_at ON tasks(created_at);
CREATE INDEX idx_tasks_priority ON tasks(priority);
```

### Task Logs Table
```sql
CREATE TABLE task_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    level VARCHAR(10) NOT NULL, -- 'DEBUG', 'INFO', 'WARNING', 'ERROR'
    message TEXT NOT NULL,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_task_logs_task_id ON task_logs(task_id);
CREATE INDEX idx_task_logs_created_at ON task_logs(created_at);
```

### User Sessions Table
```sql
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id),
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
```

## 2. Task Queue System

### Queue Configuration
```python
# Task queue settings
MAX_CONCURRENT_TASKS = 5
MAX_QUEUE_SIZE = 100
TASK_TIMEOUT = 3600  # 1 hour
CLEANUP_INTERVAL = 300  # 5 minutes

# Priority levels
PRIORITY_HIGH = 1
PRIORITY_NORMAL = 5
PRIORITY_LOW = 10
```

### Queue Management
- **FIFO with Priority**: Higher priority tasks run first
- **Concurrency Limits**: Maximum 5 concurrent analysis tasks
- **Resource Monitoring**: Monitor CPU/memory usage
- **Auto-scaling**: Adjust concurrency based on system load
- **Retry Logic**: Failed tasks automatically retry with exponential backoff

## 3. User Authentication System

### Authentication Methods
1. **JWT Tokens**: For API access
2. **Session Cookies**: For web interface
3. **API Keys**: For programmatic access
4. **OAuth Integration**: Google/GitHub login

### Authorization Levels
- **Admin**: Full system access
- **User**: Access to own sites and tasks
- **Viewer**: Read-only access to assigned sites
- **API User**: Programmatic access with rate limits

### Rate Limiting
- **Per User**: 10 requests/minute for analysis
- **Per IP**: 100 requests/minute total
- **Per API Key**: Configurable limits

## 4. Enhanced API Endpoints

### Task Management Endpoints
```python
# List user's tasks
GET /api/v1/tasks/
GET /api/v1/tasks/?status=running&limit=10

# Get specific task
GET /api/v1/tasks/{task_id}

# Cancel task
DELETE /api/v1/tasks/{task_id}

# Retry failed task
POST /api/v1/tasks/{task_id}/retry

# Get task logs
GET /api/v1/tasks/{task_id}/logs

# Get queue status
GET /api/v1/queue/status
```

### User Management Endpoints
```python
# Authentication
POST /api/v1/auth/login
POST /api/v1/auth/logout
POST /api/v1/auth/refresh
GET /api/v1/auth/me

# User management
GET /api/v1/users/profile
PUT /api/v1/users/profile
GET /api/v1/users/tasks
GET /api/v1/users/sites
```

## 5. Enhanced Frontend Features

### Task Dashboard
- **Real-time task list** with status updates
- **Progress bars** for running tasks
- **Task history** with filtering and search
- **Bulk operations** (cancel multiple tasks)
- **Task details modal** with logs and configuration

### Multi-task Management
- **Queue multiple analyses** for different sites
- **Batch operations** (analyze all sites)
- **Task scheduling** (run analysis daily/weekly)
- **Notification system** (email/browser notifications)

### User Interface Improvements
- **User login/logout** functionality
- **Site permissions** management
- **Task templates** for common configurations
- **Export task results** to various formats

## 6. Logging and Monitoring

### Task-Specific Logging
```python
class TaskLogger:
    def __init__(self, task_id: str, user_id: str):
        self.task_id = task_id
        self.user_id = user_id
    
    def log(self, level: str, message: str, details: dict = None):
        # Save to database
        # Send to monitoring system
        # Update task progress
```

### Monitoring Features
- **Real-time metrics** (active tasks, queue length, success rate)
- **Performance tracking** (task duration, resource usage)
- **Error tracking** (failed tasks, error patterns)
- **User activity** (login frequency, task patterns)

## 7. Implementation Plan

### Phase 1: Database and Authentication (Week 1)
1. Create database schema
2. Implement user authentication
3. Add JWT token management
4. Create user management endpoints

### Phase 2: Task Queue System (Week 2)
1. Implement database-backed task storage
2. Create task queue manager
3. Add concurrency controls
4. Implement retry logic

### Phase 3: Enhanced API (Week 3)
1. Update all endpoints to use new task system
2. Add user authorization checks
3. Implement rate limiting
4. Add task management endpoints

### Phase 4: Frontend Updates (Week 4)
1. Add user authentication UI
2. Create task dashboard
3. Implement real-time updates
4. Add multi-task management

### Phase 5: Monitoring and Optimization (Week 5)
1. Add comprehensive logging
2. Implement monitoring dashboard
3. Performance optimization
4. Load testing and tuning

## 8. Configuration Changes

### Environment Variables
```bash
# Authentication
JWT_SECRET_KEY=your-secret-key
JWT_EXPIRATION_HOURS=24
SESSION_TIMEOUT_HOURS=8

# Task Queue
MAX_CONCURRENT_TASKS=5
MAX_QUEUE_SIZE=100
TASK_TIMEOUT_SECONDS=3600

# Rate Limiting
RATE_LIMIT_PER_USER=10
RATE_LIMIT_PER_IP=100
RATE_LIMIT_WINDOW_MINUTES=1

# Monitoring
ENABLE_METRICS=true
METRICS_ENDPOINT=/metrics
LOG_LEVEL=INFO
```

### Database Migrations
- Migration scripts for new tables
- Data migration for existing tasks
- Index creation for performance
- Cleanup of old data structures

This comprehensive system will transform the application from a simple task runner into a robust, multi-user, enterprise-ready platform with proper task management, user authentication, and monitoring capabilities.
