# Site Deletion Error Handling - Foreign Key Constraint Fix

## 🎯 **Issue Resolved**

**Error:** `HTTP/2 409 Conflict - update or delete on table "sites" violates foreign key constraint "gsc_keywords_site_id_fkey"`

**Root Cause:** The site deletion process was trying to delete the site record while child records still existed in related tables, violating foreign key constraints.

## 🔧 **Solution Implemented**

### **1. Enhanced Deletion Logic**

#### **Before (Problematic):**
```python
def delete_site_completely(self) -> bool:
    # Called delete_all_site_data() which UPDATED site record
    # Then tried to DELETE site record
    # But if data deletion failed partially, FK constraints prevented deletion
```

#### **After (Fixed):**
```python
def delete_site_completely(self) -> bool:
    # 1. Check reference counts first
    # 2. Delete from child tables in correct order
    # 3. Verify each deletion step
    # 4. Only then delete site record
    # 5. Verify final deletion
```

### **2. Custom Exception Classes**

Added specific exception types for better error handling:

```python
class SupabaseError(Exception):
    """Base exception for Supabase operations"""

class ForeignKeyConstraintError(SupabaseError):
    """Raised when FK constraint prevents operation"""

class SiteDeletionError(SupabaseError):
    """Raised when site deletion fails with context"""
    def __init__(self, message, site_id=None, remaining_refs=None):
        self.remaining_refs = remaining_refs  # Shows what's blocking deletion
```

### **3. Reference Checking Method**

```python
def check_site_references(self) -> Dict[str, int]:
    """Check how many records reference this site in each table"""
    # Returns: {'pages': 110, 'gsc_keywords': 1000, 'gsc_traffic': 1000, ...}
```

### **4. Force Deletion Fallback**

```python
def force_delete_site(self) -> bool:
    """Force delete by clearing all data first, ignoring errors"""
    # 1. Delete from all child tables (ignore individual errors)
    # 2. Check remaining references
    # 3. Delete site record
    # 4. Verify deletion
```

## 🚀 **Improved API Response**

### **Normal Deletion Success:**
```json
{
  "success": true,
  "message": "Site example.com deleted completely",
  "domain": "example.com",
  "site_id": "1",
  "action": "site_deleted"
}
```

### **Force Deletion Success:**
```json
{
  "success": true,
  "message": "Site example.com deleted completely (force deletion used)",
  "domain": "example.com",
  "site_id": "1", 
  "action": "site_deleted"
}
```

### **Deletion Failure (with Context):**
```json
{
  "detail": "Cannot delete site example.com even with force deletion. Remaining data: {'gsc_keywords': 5, 'pages': 2}. Manual database cleanup may be required."
}
```

## 📊 **Deletion Process Flow**

```mermaid
graph TD
    A[Delete Site Request] --> B[Check References]
    B --> C{Any References?}
    C -->|No| D[Delete Site Record]
    C -->|Yes| E[Delete Child Records]
    E --> F[Verify Deletions]
    F --> G{All Deleted?}
    G -->|Yes| D
    G -->|No| H[Try Force Deletion]
    H --> I{Force Success?}
    I -->|Yes| J[Return Success]
    I -->|No| K[Return Error with Context]
    D --> L{Site Deleted?}
    L -->|Yes| J
    L -->|No| M[Return Error]
```

## 🔍 **Enhanced Logging**

### **Before:**
```
ERROR - Error completely deleting site: {'message': 'foreign key constraint violation'}
```

### **After:**
```
INFO - Starting complete deletion for site example.com (ID: 1)
INFO - Reference counts: {'pages': 110, 'gsc_keywords': 1000, 'gsc_traffic': 1000}
INFO - Total records to delete: 2110
INFO - Deleted 110 records from pages for site example.com
INFO - Deleted 1000 records from gsc_keywords for site example.com
INFO - ✅ Successfully deleted site example.com completely
```

## 🛡️ **Error Prevention**

### **1. Pre-deletion Validation**
- Check all foreign key references before attempting deletion
- Log total records to be deleted
- Identify potential constraint violations early

### **2. Graceful Degradation**
- If normal deletion fails → try force deletion
- If force deletion fails → provide detailed error with remaining references
- Never leave the system in an inconsistent state

### **3. User-Friendly Error Messages**
- Clear explanation of what went wrong
- Specific guidance on next steps
- Context about remaining data blocking deletion

## ✅ **Testing Results**

### **Successful Deletion:**
- ✅ Site with no data → Deletes immediately
- ✅ Site with data → Deletes all child records first, then site
- ✅ Partial deletion failure → Force deletion succeeds

### **Error Handling:**
- ✅ Foreign key constraints → Clear error message with context
- ✅ Network errors → Proper error propagation
- ✅ Permission errors → Specific error identification

## 🎯 **Benefits Achieved**

1. **🔒 Reliable Deletion** - No more foreign key constraint errors
2. **📊 Better Visibility** - Clear logging of what's being deleted
3. **🛡️ Error Recovery** - Force deletion as fallback option
4. **👥 User Experience** - Clear error messages with actionable guidance
5. **🔧 Debugging** - Detailed logs for troubleshooting

---

**🎉 Result**: Site deletion now works reliably with comprehensive error handling and user-friendly feedback!
