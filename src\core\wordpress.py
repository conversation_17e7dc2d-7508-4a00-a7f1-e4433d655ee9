"""
WordPress API integration
"""
import requests
from typing import Optional, Dict, Any
from urllib.parse import urlparse

from src.utils.logging import get_logger

logger = get_logger(__name__)


class WordPressAPIClient:
    """Client for WordPress Data Exporter API"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
    
    def detect_wp_api(self, domain: str, homepage: Optional[str] = None) -> Optional[str]:
        """Detect WordPress API endpoint for a domain and optional homepage"""
        # Try both domain and homepage URLs
        urls_to_try = []

        # Add domain URL
        parsed_domain = urlparse(domain)
        domain_base_url = f"{parsed_domain.scheme}://{parsed_domain.netloc}"
        urls_to_try.append(domain_base_url)

        # Add homepage URL if different from domain
        if homepage and homepage != domain:
            parsed_homepage = urlparse(homepage)
            homepage_base_url = f"{parsed_homepage.scheme}://{parsed_homepage.netloc}"
            if homepage_base_url != domain_base_url:
                urls_to_try.append(homepage_base_url)

        # Common WordPress API endpoints to try
        endpoint_paths = [
            "/wp-json/wp-data-exporter/v1/data"  # Your specific endpoint
      
        ]

        # Build all combinations of base URLs and endpoint paths
        endpoints = []
        for base_url in urls_to_try:
            for path in endpoint_paths:
                endpoints.append(f"{base_url}{path}")

        logger.info(f"Trying WordPress API detection for URLs: {urls_to_try}")
        
        for endpoint in endpoints:
            try:
                logger.info(f"Trying WordPress API endpoint: {endpoint}")


                headers = {'X-Plugin-API-Key': self.api_key}
                response = requests.head(endpoint, headers=headers, timeout=10)


                logger.info(f"Response status for {endpoint}: {response.status_code}")
                if response.status_code in [200, 405]:  # 405 is method not allowed but endpoint exists
                    logger.info(f"Found WordPress API endpoint: {endpoint}")
                    return endpoint
            except requests.RequestException as e:
                logger.info(f"Failed to connect to {endpoint}: {e}")
                continue
        
        logger.warning(f"No WordPress API endpoint found for {domain}")
        return None
    
    def fetch_data(self, wp_api_url: str, modified_since: str = '', modified_until: str = '') -> Optional[Dict[str, Any]]:
        """Fetch data from WordPress API with automatic pagination and date filtering"""
        logger.info(f"Fetching data from WordPress API: {wp_api_url}")

        if modified_since or modified_until:
            logger.info(f"Using date range filter: {modified_since} to {modified_until}")

        # First, get total counts to determine if pagination is needed
        counts_data = self.fetch_counts(wp_api_url, modified_since, modified_until)
        if not counts_data:
            logger.warning("Could not fetch counts from WordPress API, falling back to single request")
            return self._fetch_single_page(wp_api_url, modified_since=modified_since, modified_until=modified_until)

        total_pages = counts_data.get('total_counts', {}).get('total_pages', 0)
        date_range_info = counts_data.get('total_counts', {}).get('date_range', {})

        if modified_since or modified_until:
            logger.info(f"WordPress site has {total_pages} pages modified in date range")
        else:
            logger.info(f"WordPress site has {total_pages} total pages")

        if total_pages == 0:
            logger.info("No pages found in WordPress site for the specified criteria")
            return {'publish_seo_data': [], 'internal_links_data': [], 'date_range': date_range_info}

        # Determine optimal pagination strategy
        per_page = min(100, max(50, total_pages // 10))  # Adaptive batch size
        total_requests = (total_pages + per_page - 1) // per_page  # Ceiling division

        logger.info(f"Using pagination: {per_page} pages per request, {total_requests} total requests")

        # Fetch all data with pagination
        all_pages_data = []
        all_links_data = []

        for page in range(1, total_requests + 1):
            logger.info(f"Fetching page {page}/{total_requests} from WordPress API...")

            page_data = self._fetch_single_page(
                wp_api_url, page=page, per_page=per_page, data_type='all',
                modified_since=modified_since, modified_until=modified_until
            )
            if not page_data:
                logger.warning(f"Failed to fetch page {page}, continuing with remaining pages")
                continue

            # Accumulate data
            if 'publish_seo_data' in page_data:
                all_pages_data.extend(page_data['publish_seo_data'])
            if 'internal_links_data' in page_data:
                all_links_data.extend(page_data['internal_links_data'])

        logger.info(f"Successfully fetched {len(all_pages_data)} pages and {len(all_links_data)} links from WordPress API")

        return {
            'publish_seo_data': all_pages_data,
            'internal_links_data': all_links_data,
            'site_url': counts_data.get('site_url'),
            'generated_at': counts_data.get('generated_at'),
            'total_pages_fetched': len(all_pages_data),
            'total_links_fetched': len(all_links_data),
            'date_range': date_range_info
        }

    def fetch_counts(self, wp_api_url: str, modified_since: str = '', modified_until: str = '') -> Optional[Dict[str, Any]]:
        """Fetch total counts from WordPress API with optional date filtering"""
        counts_url = wp_api_url.replace('/data', '/data/counts')
        logger.info(f"Fetching counts from WordPress API: {counts_url}")

        headers = {'X-Plugin-API-Key': self.api_key}
        params = {}

        if modified_since:
            params['modified_since'] = modified_since
        if modified_until:
            params['modified_until'] = modified_until

        try:
            response = requests.get(counts_url, headers=headers, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            total_counts = data.get('total_counts', {})
            logger.info(f"WordPress API counts: {total_counts.get('total_pages', 0)} pages")
            if modified_since or modified_until:
                logger.info(f"Date range: {modified_since} to {modified_until}")
            return data
        except requests.exceptions.RequestException as e:
            logger.warning(f"Could not fetch counts from WordPress API: {e}")
            return None

    def _fetch_single_page(self, wp_api_url: str, page: int = 1, per_page: int = 50,
                          data_type: str = 'all', modified_since: str = '', modified_until: str = '') -> Optional[Dict[str, Any]]:
        """Fetch a single page of data from WordPress API with optional date filtering"""
        headers = {'X-Plugin-API-Key': self.api_key}
        params = {
            'page': page,
            'per_page': per_page,
            'data_type': data_type
        }

        if modified_since:
            params['modified_since'] = modified_since
        if modified_until:
            params['modified_until'] = modified_until

        try:
            response = requests.get(wp_api_url, headers=headers, params=params, timeout=60)
            logger.debug(f"WordPress API request: {response.url}")
            response.raise_for_status()

            # Handle responses that may have PHP warnings/errors before JSON
            response_text = response.text

            # Check if response starts with HTML/PHP errors
            if response_text.strip().startswith('<'):
                logger.warning("WordPress API response contains HTML/PHP errors, attempting to extract JSON")

                # Try to find JSON start
                json_start = response_text.find('{')
                if json_start != -1:
                    # Extract JSON portion
                    json_text = response_text[json_start:]
                    logger.debug(f"Extracted JSON from position {json_start}")

                    # Try to parse the extracted JSON
                    import json
                    data = json.loads(json_text)
                else:
                    logger.error("Could not find JSON start in response with HTML errors")
                    return None
            else:
                # Normal JSON response
                data = response.json()

            # Log response details for debugging
            if isinstance(data, dict):
                if 'internal_links_data' in data:
                    logger.debug(f"Page {page}: Found {len(data['internal_links_data'])} internal links")
                if 'publish_seo_data' in data:
                    logger.debug(f"Page {page}: Found {len(data['publish_seo_data'])} pages")

            return data
        except requests.exceptions.RequestException as e:
            logger.error(f"Could not fetch page {page} from WordPress API: {e}")
            return None
