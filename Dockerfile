FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p reports temp logs

# Set permissions
RUN chmod 755 reports temp logs

# Expose port
EXPOSE 8000

# Start command
CMD ["uvicorn", "api_refactored:app", "--host", "0.0.0.0", "--port", "8000"]
