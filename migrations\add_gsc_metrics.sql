-- Database Migration: Add CTR and Position columns to pages table
-- Run this in your Supabase SQL Editor

-- Add CTR and Position columns to pages table
ALTER TABLE pages 
ADD COLUMN IF NOT EXISTS "CTR" NUMERIC,
ADD COLUMN IF NOT EXISTS "Position" NUMERIC;

-- Add comments to document the new columns
COMMENT ON COLUMN pages."CTR" IS 'Click-through rate calculated from GSC data (clicks/impressions)';
COMMENT ON COLUMN pages."Position" IS 'Weighted average position from GSC data';

-- Update existing records to have default values (0.0) for the new columns
UPDATE pages 
SET "CTR" = 0.0 
WHERE "CTR" IS NULL;

UPDATE pages 
SET "Position" = 0.0 
WHERE "Position" IS NULL;

-- Create indexes for better performance on the new columns
CREATE INDEX IF NOT EXISTS idx_pages_ctr ON pages("CTR");
CREATE INDEX IF NOT EXISTS idx_pages_position ON pages("Position");

-- Verify the changes
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'pages' 
AND column_name IN ('CTR', 'Position')
ORDER BY column_name;
