# SEO Site Manager API Documentation

## Base URL
```
http://localhost:8000
```

## Authentication
No authentication required. Supabase credentials are automatically loaded from environment variables.

## Endpoints

### 1. List Sites
**GET** `/sites/`

Lists all analyzed sites with data summaries.

**Response:**
```json
{
  "sites": [
    {
      "domain": "example.com",
      "site_id": "1",
      "created_at": "2025-06-24T10:00:00Z",
      "data_summary": {
        "pages": 110,
        "keywords": 1000,
        "traffic_records": 1000,
        "internal_links": 0,
        "analytics_records": 0,
        "total_records": 2110
      },
      "available_dates": ["2025-06-24"],
      "available_months": ["2024-06", "2024-07", "2024-08"],
      "last_updated": "2025-06-24"
    }
  ],
  "total_sites": 1
}
```

### 2. Add New Site / Re-run Analysis
**POST** `/generate_report_with_service_account/`

Analyzes a new site or re-analyzes an existing site and stores updated data in Supabase.

**Request Body:**
```json
{
  "domain_property": "https://example.com/",
  "ga_property_id": "*********",
  "start_date": "2024-01-01",
  "end_date": "2024-12-31",
  "service_account_data": ***********************************************************************************************************************************************************************************************************************************************************************************************************
}
```

**Response:**
```json
{
  "task_id": "uuid-string",
  "status": "running",
  "message": "Analysis started"
}
```

### 3. Generate Excel Report
**POST** `/generate_excel_enhanced/`

Generates Excel report from existing Supabase data.

**Request Body:**
```json
{
  "site_id": "1",
  "start_date": "2024-01-01",
  "end_date": "2024-12-31",
  "include_raw_data": true,
  "include_keywords": true,
  "include_traffic": true,
  "include_links": true,
  "include_analytics": true
}
```

**Alternative (using domain):**
```json
{
  "domain": "example.com",
  "date": "2024-06-24"
}
```

**Response:**
```json
{
  "task_id": "uuid-string",
  "status": "running",
  "message": "Excel generation started"
}
```

### 4. Check Task Status
**GET** `/task/{task_id}`

Checks the status of a running task.

**Response:**
```json
{
  "task_id": "uuid-string",
  "status": "completed",
  "progress": 100,
  "message": "Analysis completed successfully",
  "result": {
    "excel_report": "path/to/report.xlsx",
    "domain": "example.com"
  }
}
```

**Status Values:**
- `running` - Task in progress
- `completed` - Task finished successfully
- `failed` - Task failed with error

### 5. Download Report
**GET** `/download/{filename}`

Downloads a generated report file.

**Response:** File download

### 6. Get Site Data Info
**GET** `/supabase_data/{domain}`

Gets detailed information about available data for a specific domain.

**Response:**
```json
{
  "domain": "example.com",
  "site_id": "1",
  "data_summary": {
    "pages": 110,
    "keywords": 1000,
    "traffic_records": 1000,
    "internal_links": 0,
    "analytics_records": 0
  },
  "available_dates": ["2025-06-24"],
  "available_months": ["2024-06", "2024-07"]
}
```

## Usage Examples

### Using cURL

#### List all sites:
```bash
curl -X GET http://localhost:8000/sites/
```

#### Generate Excel report:
```bash
curl -X POST http://localhost:8000/generate_excel_enhanced/ \
  -H "Content-Type: application/json" \
  -d '{
    "site_id": "1",
    "start_date": "2024-01-01",
    "end_date": "2024-12-31"
  }'
```

#### Check task status:
```bash
curl -X GET http://localhost:8000/task/your-task-id
```

### Using Python

```python
import requests
import json

# List sites
response = requests.get('http://localhost:8000/sites/')
sites = response.json()

# Generate Excel report
data = {
    "site_id": "1",
    "start_date": "2024-01-01",
    "end_date": "2024-12-31"
}
response = requests.post(
    'http://localhost:8000/generate_excel_enhanced/',
    headers={'Content-Type': 'application/json'},
    data=json.dumps(data)
)
task = response.json()

# Check task status
response = requests.get(f'http://localhost:8000/task/{task["task_id"]}')
status = response.json()
```

### Using JavaScript

```javascript
// List sites
const sites = await fetch('http://localhost:8000/sites/')
  .then(r => r.json());

// Generate Excel report
const task = await fetch('http://localhost:8000/generate_excel_enhanced/', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    site_id: '1',
    start_date: '2024-01-01',
    end_date: '2024-12-31'
  })
}).then(r => r.json());

// Check task status
const status = await fetch(`http://localhost:8000/task/${task.task_id}`)
  .then(r => r.json());
```

## Error Handling

All endpoints return appropriate HTTP status codes:
- `200` - Success
- `400` - Bad Request (invalid parameters)
- `404` - Not Found (site/task not found)
- `422` - Validation Error (invalid data format)
- `500` - Internal Server Error

Error responses include details:
```json
{
  "detail": "Error description"
}
```

## Environment Setup

Ensure these environment variables are set:
```env
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-supabase-key
API_HOST=localhost
API_PORT=8000
```

## Notes

- All endpoints work independently of the web interface
- Supabase credentials are automatically loaded from environment variables
- Tasks are processed asynchronously - use task status endpoint to monitor progress
- Generated files are temporarily stored and accessible via download endpoint
- Date formats should be YYYY-MM-DD
- Service account data should be valid Google Cloud service account JSON
