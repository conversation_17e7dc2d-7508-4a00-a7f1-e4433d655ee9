#!/usr/bin/env python3
"""
Test Organization Script

This script organizes test files into a proper test structure:
1. Creates proper test directory structure
2. Moves test files to appropriate locations
3. Creates test documentation
4. Generates test runner scripts
5. Creates pytest configuration
"""

import os
import shutil
import re
from pathlib import Path
from datetime import datetime

# Define the project root
PROJECT_ROOT = Path(os.path.dirname(os.path.abspath(__file__)))

# Define test categories and their files
TEST_CATEGORIES = {
    "unit": {
        "description": "Unit tests for individual components",
        "files": [
            ("test_data_types_fix.py", "test_data_types.py"),
            ("test_raw_html_removal.py", "test_text_processing.py"),
        ]
    },
    "integration": {
        "description": "Integration tests for system components",
        "files": [
            ("test_excel_gsc_metrics.py", "test_excel_generation.py"),
            ("test_enhanced_reports.py", "test_report_service.py"),
            ("test_wordpress_pagination.py", "test_wordpress_api.py"),
            ("test_ga_aggregation_fix.py", "test_ga_aggregation.py"),
        ]
    },
    "functional": {
        "description": "End-to-end functional tests",
        "files": [
            ("test_data_sheet_columns.py", "test_data_sheet_generation.py"),
            ("test_pages_save_debug.py", "test_database_operations.py"),
        ]
    },
    "debug": {
        "description": "Debug and troubleshooting scripts",
        "files": [
            ("test_clear_data.py", "debug_clear_data.py"),
            ("debug_clear_data.py", "debug_clear_data.py"),
            ("debug_database_save_issue.py", "debug_database_save.py"),
            ("debug_file_upload_issue.py", "debug_file_upload.py"),
            ("debug_pages_upsert_issue.py", "debug_pages_upsert.py"),
            ("fix_ga_data_in_pages.py", "debug_ga_data_fix.py"),
        ]
    }
}

# Define test directory structure
TEST_DIRS = [
    "tests",
    "tests/unit",
    "tests/integration", 
    "tests/functional",
    "tests/debug",
    "tests/fixtures",
    "tests/conftest"
]

def create_test_directories():
    """Create the test directory structure"""
    print("Creating test directories...")
    
    for dir_path in TEST_DIRS:
        full_path = PROJECT_ROOT / dir_path
        if not full_path.exists():
            full_path.mkdir(parents=True)
            print(f"  ✅ Created {dir_path}/")
        else:
            print(f"  ℹ️ {dir_path}/ already exists")

def organize_test_files():
    """Organize test files into appropriate categories"""
    print("\nOrganizing test files...")
    
    moved_files = []
    
    for category, info in TEST_CATEGORIES.items():
        print(f"\n  📁 {category.upper()} - {info['description']}")
        
        category_dir = PROJECT_ROOT / "tests" / category
        category_dir.mkdir(exist_ok=True)
        
        for source_file, dest_file in info['files']:
            source_path = PROJECT_ROOT / source_file
            dest_path = category_dir / dest_file
            
            if not source_path.exists():
                print(f"    ⚠️ {source_file} not found, skipping")
                continue
            
            try:
                # Create backup if destination exists
                if dest_path.exists():
                    backup_path = dest_path.with_suffix(f".bak.{datetime.now().strftime('%Y%m%d%H%M%S')}")
                    shutil.copy2(dest_path, backup_path)
                    print(f"    ℹ️ Backed up existing {dest_file}")
                
                # Copy file to test directory
                shutil.copy2(source_path, dest_path)
                print(f"    ✅ Moved {source_file} → tests/{category}/{dest_file}")
                
                # Add to list for later deletion
                moved_files.append(source_path)
                
            except Exception as e:
                print(f"    ❌ Error moving {source_file}: {e}")
    
    return moved_files

def create_test_documentation():
    """Create comprehensive test documentation"""
    print("\nCreating test documentation...")
    
    test_readme_content = """# Tests

This directory contains all tests for the SEO Analysis Tool, organized by category.

## Test Structure

```
tests/
├── unit/           # Unit tests for individual components
├── integration/    # Integration tests for system components  
├── functional/     # End-to-end functional tests
├── debug/          # Debug and troubleshooting scripts
├── fixtures/       # Test data and fixtures
└── conftest/       # Pytest configuration and shared fixtures
```

## Test Categories

### Unit Tests (`tests/unit/`)
Tests for individual functions and classes in isolation.

- `test_data_types.py` - Data type validation and conversion tests
- `test_text_processing.py` - HTML/text processing utility tests

### Integration Tests (`tests/integration/`)
Tests for interactions between components.

- `test_excel_generation.py` - Excel report generation tests
- `test_report_service.py` - Report service integration tests
- `test_wordpress_api.py` - WordPress API integration tests
- `test_ga_aggregation.py` - Google Analytics data aggregation tests

### Functional Tests (`tests/functional/`)
End-to-end tests that verify complete workflows.

- `test_data_sheet_generation.py` - Complete data sheet generation workflow
- `test_database_operations.py` - Database save/retrieve operations

### Debug Scripts (`tests/debug/`)
Troubleshooting and debugging utilities.

- `debug_clear_data.py` - Clear test data from database
- `debug_database_save.py` - Debug database save issues
- `debug_file_upload.py` - Debug file upload problems
- `debug_pages_upsert.py` - Debug page insertion issues
- `debug_ga_data_fix.py` - Debug GA data aggregation

## Running Tests

### Run All Tests
```bash
python -m pytest tests/
```

### Run Specific Category
```bash
# Unit tests only
python -m pytest tests/unit/

# Integration tests only
python -m pytest tests/integration/

# Functional tests only
python -m pytest tests/functional/
```

### Run Specific Test File
```bash
python -m pytest tests/unit/test_data_types.py
```

### Run with Coverage
```bash
python -m pytest tests/ --cov=src --cov-report=html
```

### Run Debug Scripts
```bash
# Clear test data
python tests/debug/debug_clear_data.py

# Debug database issues
python tests/debug/debug_database_save.py
```

## Test Configuration

Tests use pytest with the following configuration:
- Automatic test discovery
- Fixtures for common test data
- Coverage reporting
- Parallel test execution (when possible)

## Writing New Tests

### Unit Tests
- Test individual functions/methods
- Mock external dependencies
- Focus on edge cases and error conditions

### Integration Tests  
- Test component interactions
- Use real dependencies when possible
- Verify data flow between components

### Functional Tests
- Test complete user workflows
- Use realistic test data
- Verify end-to-end functionality

## Test Data

Test fixtures and data are stored in `tests/fixtures/`:
- Sample configuration files
- Mock API responses
- Test database schemas
- Example input/output data

## Continuous Integration

Tests are automatically run on:
- Pull requests
- Main branch commits
- Release builds

All tests must pass before code can be merged.
"""
    
    test_readme_path = PROJECT_ROOT / "tests" / "README.md"
    with open(test_readme_path, 'w', encoding='utf-8') as f:
        f.write(test_readme_content)
    
    print("  ✅ Created tests/README.md")

def create_pytest_config():
    """Create pytest configuration files"""
    print("\nCreating pytest configuration...")
    
    # Create pytest.ini
    pytest_ini_content = """[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=src
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=80

markers =
    unit: Unit tests
    integration: Integration tests
    functional: Functional tests
    slow: Slow running tests
    requires_db: Tests that require database connection
    requires_api: Tests that require external API access
"""
    
    pytest_ini_path = PROJECT_ROOT / "pytest.ini"
    with open(pytest_ini_path, 'w') as f:
        f.write(pytest_ini_content)
    
    print("  ✅ Created pytest.ini")
    
    # Create conftest.py
    conftest_content = """\"\"\"
Pytest configuration and shared fixtures
\"\"\"
import pytest
import os
import sys
from pathlib import Path

# Add src to path for imports
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT / 'src'))

@pytest.fixture(scope="session")
def project_root():
    \"\"\"Project root directory\"\"\"
    return PROJECT_ROOT

@pytest.fixture(scope="session") 
def test_data_dir():
    \"\"\"Test data directory\"\"\"
    return PROJECT_ROOT / "tests" / "fixtures"

@pytest.fixture
def sample_config():
    \"\"\"Sample configuration for testing\"\"\"
    return {
        "domain_property": "https://example.com/",
        "ga_property_id": "123456789",
        "start_date": "2024-01-01",
        "end_date": "2024-12-31"
    }

@pytest.fixture
def mock_supabase_client():
    \"\"\"Mock Supabase client for testing\"\"\"
    from unittest.mock import Mock
    return Mock()

# Skip tests that require external services in CI
def pytest_configure(config):
    config.addinivalue_line(
        "markers", "requires_external: mark test as requiring external services"
    )

def pytest_collection_modifyitems(config, items):
    if config.getoption("--no-external"):
        skip_external = pytest.mark.skip(reason="--no-external option given")
        for item in items:
            if "requires_external" in item.keywords:
                item.add_marker(skip_external)
"""
    
    conftest_path = PROJECT_ROOT / "tests" / "conftest.py"
    with open(conftest_path, 'w') as f:
        f.write(conftest_content)
    
    print("  ✅ Created tests/conftest.py")

def create_test_runner():
    """Create test runner script"""
    print("\nCreating test runner...")
    
    runner_content = """#!/usr/bin/env python3
\"\"\"
Test Runner Script

Provides convenient commands for running different types of tests.
\"\"\"

import sys
import subprocess
from pathlib import Path

def run_command(cmd):
    \"\"\"Run a command and return the result\"\"\"
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    print(result.stdout)
    if result.stderr:
        print(result.stderr)
    return result.returncode == 0

def main():
    if len(sys.argv) < 2:
        print("Usage: python run_tests.py [command]")
        print("Commands:")
        print("  all        - Run all tests")
        print("  unit       - Run unit tests only")
        print("  integration - Run integration tests only") 
        print("  functional - Run functional tests only")
        print("  coverage   - Run tests with coverage report")
        print("  fast       - Run tests excluding slow ones")
        print("  debug      - Run debug scripts")
        return
    
    command = sys.argv[1]
    
    if command == "all":
        success = run_command(["python", "-m", "pytest", "tests/"])
    elif command == "unit":
        success = run_command(["python", "-m", "pytest", "tests/unit/"])
    elif command == "integration":
        success = run_command(["python", "-m", "pytest", "tests/integration/"])
    elif command == "functional":
        success = run_command(["python", "-m", "pytest", "tests/functional/"])
    elif command == "coverage":
        success = run_command(["python", "-m", "pytest", "tests/", "--cov=src", "--cov-report=html"])
    elif command == "fast":
        success = run_command(["python", "-m", "pytest", "tests/", "-m", "not slow"])
    elif command == "debug":
        print("Available debug scripts:")
        debug_dir = Path("tests/debug")
        if debug_dir.exists():
            for script in debug_dir.glob("*.py"):
                print(f"  python {script}")
        success = True
    else:
        print(f"Unknown command: {command}")
        success = False
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
"""
    
    runner_path = PROJECT_ROOT / "run_tests.py"
    with open(runner_path, 'w') as f:
        f.write(runner_content)
    
    print("  ✅ Created run_tests.py")

def main():
    """Main function to organize tests"""
    print("=" * 60)
    print("🧪 Test Organization Script")
    print("=" * 60)
    
    print("\nThis script will organize test files into a proper structure:")
    print("1. Create test directory structure")
    print("2. Move test files to appropriate categories")
    print("3. Create test documentation")
    print("4. Generate pytest configuration")
    print("5. Create test runner scripts")
    
    confirm = input("\nDo you want to proceed? (y/n): ")
    if confirm.lower() != 'y':
        print("Test organization cancelled.")
        return
    
    # Create test directories
    create_test_directories()
    
    # Organize test files
    moved_files = organize_test_files()
    
    # Create documentation
    create_test_documentation()
    
    # Create pytest configuration
    create_pytest_config()
    
    # Create test runner
    create_test_runner()
    
    print("\n" + "=" * 60)
    print("✅ Test organization completed successfully!")
    print("=" * 60)
    
    print("\nTest structure created:")
    print("  tests/unit/        - Unit tests")
    print("  tests/integration/ - Integration tests")
    print("  tests/functional/  - Functional tests")
    print("  tests/debug/       - Debug scripts")
    print("  tests/fixtures/    - Test data")
    
    print("\nTo run tests:")
    print("  python run_tests.py all       # All tests")
    print("  python run_tests.py unit      # Unit tests only")
    print("  python run_tests.py coverage  # With coverage")
    
    print("\nNext steps:")
    print("1. Review organized test files")
    print("2. Update test imports if needed")
    print("3. Run tests to verify they work")
    print("4. Delete original test files if satisfied")
    
    return moved_files

if __name__ == "__main__":
    main()
