-- Add cached statistics columns to sites table for fast dashboard loading
-- This eliminates the need to count records in data tables every time

-- Add statistics columns
ALTER TABLE sites 
ADD COLUMN IF NOT EXISTS stats_pages INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS stats_keywords INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS stats_internal_links INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS stats_traffic_records INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS stats_external_links INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS stats_last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Add index for faster queries
CREATE INDEX IF NOT EXISTS idx_sites_stats_last_updated ON sites(stats_last_updated);

-- Add comments for documentation
COMMENT ON COLUMN sites.stats_pages IS 'Cached count of pages for this site (updated after analysis)';
COMMENT ON COLUMN sites.stats_keywords IS 'Cached count of GSC keywords for this site (updated after analysis)';
COMMENT ON COLUMN sites.stats_internal_links IS 'Cached count of internal links for this site (updated after analysis)';
COMMENT ON COLUMN sites.stats_traffic_records IS 'Cached count of traffic records for this site (updated after analysis)';
COMMENT ON COLUMN sites.stats_external_links IS 'Cached count of external links for this site (updated after analysis)';
COMMENT ON COLUMN sites.stats_last_updated IS 'Timestamp when statistics were last updated';
