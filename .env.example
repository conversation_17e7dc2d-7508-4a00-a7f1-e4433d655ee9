# SEO Analysis Tool Environment Configuration

# API Settings
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=false

# File paths
REPORTS_DIR=reports
TEMP_DIR=temp

# Google API Settings
GOOGLE_API_TIMEOUT=60

# Crawling Settings
CRAWL_TIMEOUT=10
JS_RENDER_TIMEOUT=60000
MAX_CONCURRENT_REQUESTS=5

# Supabase Settings (optional)
SUPABASE_URL=
SUPABASE_KEY=

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
