"""
Text processing utilities
"""
import re
from bs4 import BeautifulSoup
from markdownify import markdownify as md
from typing import Optional


def html_to_markdown(html_content: str) -> str:
    """
    Converts HTML to clean Markdown using markdownify, removing unnecessary elements.

    Args:
        html_content: Raw HTML content

    Returns:
        str: Clean markdown content
    """
    if not html_content:
        return ""

    # Parse the HTML
    soup = BeautifulSoup(html_content, 'html.parser')

    # Remove unwanted elements
    for element in soup.select('script, style, nav, footer, header, .sidebar, .widget, .menu, .comments, .comment-form, .footer, .header'):
        element.decompose()

    # Focus on main content
    main_content = soup.find('main') or soup.find('article') or soup.find('div', class_='content') or soup

    # Convert the remaining HTML to Markdown
    markdown = md(str(main_content), strip=['a', 'img'])  # Optional: strip links and images

    # Remove excessive newlines
    markdown = re.sub(r'\n{3,}', '\n\n', markdown)

    return markdown


def clean_text(text: str) -> str:
    """Clean and normalize text content"""
    if not text:
        return ""
    
    # Remove excessive whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Remove leading/trailing whitespace
    text = text.strip()
    
    return text


def extract_keywords(text: str, min_length: int = 3) -> list:
    """Extract potential keywords from text"""
    if not text:
        return []
    
    # Simple keyword extraction - split by common delimiters
    words = re.findall(r'\b[a-zA-Z]{' + str(min_length) + ',}\b', text.lower())
    
    # Remove common stop words
    stop_words = {
        'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
        'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
        'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these',
        'those', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have',
        'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
        'may', 'might', 'must', 'can', 'shall'
    }
    
    keywords = [word for word in words if word not in stop_words]
    
    return list(set(keywords))  # Remove duplicates


def calculate_text_similarity(text1: str, text2: str) -> float:
    """Calculate simple text similarity based on common words"""
    if not text1 or not text2:
        return 0.0
    
    words1 = set(extract_keywords(text1))
    words2 = set(extract_keywords(text2))
    
    if not words1 or not words2:
        return 0.0
    
    intersection = words1.intersection(words2)
    union = words1.union(words2)
    
    return len(intersection) / len(union) if union else 0.0
