"""
Pytest configuration and shared fixtures
"""
import pytest
import os
import sys
from pathlib import Path

# Add src to path for imports
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT / 'src'))

@pytest.fixture(scope="session")
def project_root():
    """Project root directory"""
    return PROJECT_ROOT

@pytest.fixture(scope="session") 
def test_data_dir():
    """Test data directory"""
    return PROJECT_ROOT / "tests" / "fixtures"

@pytest.fixture
def sample_config():
    """Sample configuration for testing"""
    return {
        "domain_property": "https://example.com/",
        "ga_property_id": "123456789",
        "start_date": "2024-01-01",
        "end_date": "2024-12-31"
    }

@pytest.fixture
def mock_supabase_client():
    """Mock Supabase client for testing"""
    from unittest.mock import Mock
    return Mock()

# Skip tests that require external services in CI
def pytest_configure(config):
    config.addinivalue_line(
        "markers", "requires_external: mark test as requiring external services"
    )

def pytest_collection_modifyitems(config, items):
    if config.getoption("--no-external"):
        skip_external = pytest.mark.skip(reason="--no-external option given")
        for item in items:
            if "requires_external" in item.keywords:
                item.add_marker(skip_external)
